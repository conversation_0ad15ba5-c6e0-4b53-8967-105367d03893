<template>
  <page-content
    class="news"
    ref="view"
    :status="status"
    :refreshAction="refreshAction"
    @refresh="refresh"
    @scroll="onScroll"
    @scroll-bottom="loadMore"
    @reload="reload"
  >
    <template v-if="status == AppStatus.READY">
      <div v-if="home.news.length" class="panel car-tech">
        <div class="panel-content">
          <template v-if="home.news.length">
            <news-item
              v-for="(item, index) in home.news"
              :class="{ read: hasRead(item) }"
              @click.native="pageTo(item)"
              :item="item"
              :key="index"
            ></news-item>
          </template>
          <!-- <div v-if="!home.news.length" class="list-empty">此分类下暂无内容</div> -->
        </div>
      </div>
      <list-placeholder v-else icon="~@/assets/images/mall/empty.png"
        >此栏目下暂无内容</list-placeholder
      >
      <list-loader
        v-if="home.news.length > 2"
        :options="$_loader_options"
        @load="loadMore"
      ></list-loader>
      <!-- <div :class="{'scroll-loading': true, 'scroll-loading-show':(scroll.busy || scroll.end) && home.news.length > 5}" v-html="scroll.text"></div> -->
    </template>
  </page-content>
</template>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.car-tech {
  background: white;
  margin-top: 0;
  .panel-title {
    color: #656565;
    padding-left: 13px;
  }
  .panel-content {
    padding-top: 0;
    padding-bottom: 0;
  }
}
</style>
<script>
import { formatDate, getAbsolutePath } from '@/utils';
import { toast, dialog, loading } from '@/bus';
import { getNewsListInsurance } from '@/api';
import { AppStatus, NativeView } from '@/enums';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import NewsItem from './NewsItem.vue';
import { setTitle } from '@/bridge';

const ReadStatus = {
  READ: 2,
};

const PAGE_SIZE = 10;
export default {
  name: 'NewsPanelItemInsurance',
  props: {
    type: Number,
    index: Number,
    sw: String, // 按关键词搜索
    searchIndex: [String, Number], // 需要按关键词搜索的索引
    searchTime: [Number],
    active: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    NewsItem,
  },
  mixins: [mixinLoader, mixinAuthRouter],
  data() {
    return {
      AppStatus,
      ReadStatus,
      status: AppStatus.LOADING,
      home: {
        news: [],
        types: [],
      },
      initialized: false,
      offsetTime: Date.now(),
      newsType: 0,
      refreshAction: 1,
      newsTabScrollable: false,
      readList: {},
      resumeInit: false,
    };
  },
  computed: {
    filterParams() {
      const lastNews = this.home.news.slice(-1)[0] || { t: Date.now() };
      let keyWords = '';
      if (this.sw && this.searchIndex === this.index) {
        keyWords = this.sw;
      }
      return {
        // ...this.$_loader_params,
        type: this.type,
        t: this.offsetTime,
        title: keyWords,
      };
    },
  },
  watch: {
    active(val, oldVal) {
      // 1.第一次加载
      // 2.第一次加载过了，从其他tab切换回来，此时sw应是空，resumeInit应是false
      if (val === true && !this.initialized) {
        this.init();
        return;
      }
      if (
        val === true &&
        this.initialized &&
        this.searchIndex === this.index &&
        !this.sw &&
        !this.resumeInit
      ) {
        this.offsetTime = Date.now();
        this.resumeInit = true;
        this.init();
      }
    },
    // 主要是为了识别点击动作
    searchTime(val, oldVal) {
      if (this.searchIndex === this.index) {
        this.offsetTime = Date.now();
        this.resumeInit = false; // 下次从其他tab切换回来，能清空按关键词搜索的数据
        this.init();
      }
    },
  },
  mounted() {
    if (this.active) {
      this.init();
    }
  },
  methods: {
    getNewsLogo(item) {
      return item.image || (item.showImages && item.showImages.split(',')[0]);
    },
    init() {
      this.initialized = true;
      this.initList();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    pageTo(item) {
      const theURL = item.url || item.openurl;
      this.$_router_pageTo(theURL, {
        title: '',
        titleBar: true,
        shareButton: true,
        pulldownRefresh: true,
        autoUpdateTitle: false,
        sbarBgColor: '#378DFE',
      });
      setTimeout(() => {
        this.readList[item.id] = (this.readList[item.id] || 0) + 1;
        item.status = ReadStatus.READ;
      }, 1000);
    },
    hasRead(item) {
      return this.readList[item.id] || item.status === ReadStatus.READ;
    },
    onResume() {
      console.log('home resume...');
    },
    initList() {
      // this.$_loader_setPage(1);
      // this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList().then(
        res => {
          this.status = AppStatus.READY;
          return res;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const lastNews = this.home.news.slice(-1)[0] || {};
      this.getList(lastNews.t || lastNews.time).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },
    refresh() {
      this.initList()
        .then(res => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip('刷新失败');
          this.refreshAction = Date.now();
        });
    },
    getList(t = 1) {
      const params = { ...this.filterParams, t: t === 1 ? Date.now() : t };
      return this.$_loader_bind(getNewsListInsurance, res => {
        if (t === 1) {
          this.home.news = res;
        } else {
          this.home.news = this.home.news.concat(res);
        }
        this.offsetTime = params.t;
        return res;
      }).load(params);
    },
    getNewsImage(item) {
      return item.image || (item.showImages && item.showImages.split(',')[0]);
    },
    formatDate(d, style = 'YYYY-MM-DD') {
      return formatDate(d, style);
    },
    onScroll() {},
  },
};
</script>
