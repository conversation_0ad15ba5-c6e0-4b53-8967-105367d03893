/**
 * v-debounce
 * 按钮防抖指令，可自行扩展至input
 * 接收参数：function类型
 */
export default {
  name: 'debounce',
  install(Vue, defaultOptions = {}) {
    Vue.directive('debounce', {
      bind: function (el, binding) {
        if (typeof binding.value !== 'function') {
          throw new Error('指令callback must be a function');
        }
        let timer = null;
        el.__handleClick__ = function () {
          if (timer) {
            clearInterval(timer);
          }
          timer = setTimeout(() => {
            binding.value();
          }, 500);
        };
        el.addEventListener('click', el.__handleClick__);
      },
      inserted: function (el, binding) {

      },
      update: function () {},
      componentUpdated: function () {},
      unbind: function (el, binding) {
        el.removeEventListener('click', el.__handleClick__);
      }
    });
  }
};
