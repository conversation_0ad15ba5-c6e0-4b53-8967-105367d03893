<template>
  <tabs2
    v-if="status == AppStatus.READY"
    class="news-tabs"
    :index.sync="newsTabIndex"
    :scrollable="newsTabScrollable"
    :tabs="newsTabs"
    @change="onChange"
  >
  </tabs2>
</template>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color: #ebebeb;

$menu-border-color: #dadada;

.news-tabs {
  background: white;
  box-shadow: 0 0 0 0 #dcdcdc; /* px */
  z-index: 1;
  ::v-deep .tab-item {
    transition: all 250ms;
    &::after {
      transition: all 250ms;
    }
  }
}
</style>
<script>
import { toast, dialog, loading } from '@/bus';
import { getNewsCategoriesInsurance } from '@/api';
import { AppStatus } from '@/enums';
import Tabs2 from '@/components/Tabs2.vue';

export default {
  name: 'news-nav-insurance',
  props: {
    value: [String, Number],
    tabs: Array,
  },
  components: {
    tabs2: Tabs2,
  },
  mounted() {
    this.init();
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      home: {
        news: [],
        types: [],
      },
      newsType: 0,
      newsTabIndex: this.value || 0,
      newsTabScrollable: false,
    };
  },
  computed: {
    newsTabs() {
      return this.tabs || [];
    },
  },
  watch: {
    value(val) {
      // this.newsType = val;
    },
    tabs(val) {
      if (val.length) {
        this.init();
      }
    },
  },
  methods: {
    init() {
      // this.getNewsCategory();
      if (this.newsTabs.length) {
        this.newsType = this.newsTabs[0].id;
        this.$nextTick(() => {
          this.newsTabScrollable = this.newsTabs.length > 5;
        });
      }
      if (this.$route.query.id) {
        let index = this.newsTabs.findIndex(
          item => item.id == this.$route.query.id
        );
        if (index > -1) {
          this.newsTabIndex = index;
        }
      }
      this.status = AppStatus.READY;
    },
    // getNewsCategory() {
    //   return getNewsCategoriesInsurance()
    //     .then(res => {
    //       this.home.types = res;
    //       // saveNewsCategory(res);
    //       if (res.length) {
    //         this.newsType = res[0].id;
    //         this.$nextTick(() => {
    //           this.newsTabScrollable = res.length > 5;
    //         });
    //       }
    //       if (this.$route.query.id) {
    //         let index = this.home.types.findIndex(
    //           item => item.id == this.$route.query.id
    //         );
    //         if (index > -1) {
    //           this.newsTabIndex = index;
    //         }
    //       }
    //       this.status = AppStatus.READY;
    //     })
    //     .catch(e => {
    //       console.error(e);
    //       this.status = AppStatus.ERROR;
    //     });
    // },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    setActiveIndex(index) {
      this.newsTabIndex = index;
    },
    onChange(arg) {
      // this.newsType = arg.value;
      // this.$emit('input', arg);
      this.$emit('change', arg);
    },
  },
};
</script>
