<template>
  <div class="field-row">
    <div class="options-wrap">
      <div class="option-item">
        <van-field
          v-model="photoSizeText"
          label="证件尺寸"
          is-link
          readonly
          @click="showSizePicker = true"
        />
      </div>

      <van-field
        v-model="backgroundColorText"
        label="背景颜色"
        is-link
        readonly
        @click="showColorPicker = true"
      />
      <van-popup v-model="showSizePicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="sizeOptions"
          @confirm="onSizeConfirm"
          @cancel="showSizePicker = false"
        />
      </van-popup>
      <van-popup v-model="showColorPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="colorOptions"
          @confirm="onColorConfirm"
          @cancel="showColorPicker = false"
        />
      </van-popup>

      <div class="option-item mt-15">
        <span class="switch-label">支持高清证件照</span>
        <van-switch
          v-model="isHighDefinition"
          size="24px"
          active-color="#07c160"
        />
      </div>
    </div>

    <div class="upload-wrap">
      <van-uploader
        v-model="fileList"
        :after-read="afterRead"
        :before-read="beforeRead"
        :max-count="1"
        result-type="dataUrl"
      >
        <img
          class="uploadImg"
          src="@pkg/ai/assets/images/id_photo_generate_template.png"
        />
      </van-uploader>
      <p class="upload-note">请上传的图片中只能包含一个人,多人无法进行制作!</p>
      <!-- 将重新生成按钮放在这里 -->
      <van-button
        v-if="fileList.length > 0"
        type="primary"
        class="regenerate-button"
        :loading="loading"
        loading-text="生成中..."
        @click="regeneratePhoto"
      >
        重新生成
      </van-button>
    </div>

    <!-- TODO 这里保存图片还没有经过测试 需要进行调整 -->

    <div v-if="generatedPhotos" class="generated-photos">
      <div class="photo-container">
        <h3>标准证件照</h3>
        <img
          :src="`${generatedPhotos.image_base64_standard}`"
          alt="标准证件照"
          class="generated-image"
        />
        <van-button
          type="primary"
          @click="savePhoto(generatedPhotos.image_base64_standard)"
          >保存</van-button
        >
      </div>
      <div
        v-if="isHighDefinition && generatedPhotos.image_base64_hd"
        class="photo-container"
      >
        <h3>高清证件照</h3>
        <img
          :src="`${generatedPhotos.image_base64_hd}`"
          alt="高清证件照"
          class="generated-image"
        />
        <van-button
          type="primary"
          @click="savePhoto(generatedPhotos.image_base64_hd)"
          >保存</van-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { playPhotos, saveImageBase64 } from '@/bridge';
import { getImageURL } from '@/common/image';
import { isInJglh, isInWeixin } from '@/common/env';
import { blobToBase64 } from '@/utils/image';
import { dialog, toast } from '@/bus';
import { generateIdPhoto } from '@pkg/ai/api'; // 假设这是生成证件照的API
import Compressor from 'compressorjs';

import { Uploader, Field, Popup, Picker, Switch, Button, Toast } from 'vant';

export default {
  name: 'IdPhoto',
  components: {
    [Uploader.name]: Uploader,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Switch.name]: Switch,
    [Button.name]: Button,
    [Toast.name]: Toast,
  },
  data() {
    return {
      fileList: [],
      photoSize: 'one-inch', // 默认选择一寸
      photoSizeText: '一寸', // 默认显示的文字
      isHighDefinition: true, // 默认不支持高清
      backgroundColor: '#ffffff', // 默认背景色为白色
      backgroundColorText: '白色', // 默认显示的文字
      generatedPhotos: null, // 保存生成的证件照
      imageWidth: 0,
      imageHeight: 0,
      showSizePicker: false,
      showColorPicker: false,
      loading: false,
      photoWidth: 0, // 证件照展示宽度
      photoHeight: 0, // 证件照展示高度
      sizeOptions: [
        { text: '一寸', value: 'one-inch' },
        { text: '二寸', value: 'two-inch' },
        { text: '小一寸', value: 'mini one-inch' },
        { text: '小二寸', value: 'mini two-inch' },
        { text: '大一寸', value: 'big one-inch' },
        { text: '大二寸', value: 'big two-inch' },
        { text: '五寸', value: 'five-inch' },
        { text: '教师资格证', value: 'techer-inch' },
        { text: '国家公务员考试', value: 'gongwuyuan' },
        { text: '初级会计考试', value: 'chujikuaijishi' },
        { text: '英语四六级考试', value: 'English-inch' },
        { text: '计算机等级考试', value: 'computer-inch' },
        { text: '研究生考试', value: 'yanjiusheng' },
        { text: '社保卡', value: 'shebaoka' },
        { text: '电子驾驶证', value: 'electronic-driving-license' },
        { text: '美国签证', value: 'us-qianzheng' },
        { text: '日本签证', value: 'janpan-qianzheng' },
        { text: '韩国签证', value: 'hanguo-qianzheng' },
      ],
      colorOptions: [
        { text: '白色', value: '#ffffff' },
        { text: '红色', value: '#ff0000' },
        { text: '蓝色', value: '#628bce' },
      ],
    };
  },
  computed: {
    savePicTips() {
      if (isInWeixin) {
        return '长按图片,保存到本地';
      }
      return '点击海报查看大图,长按保存';
    },
  },
  methods: {
    onSizeConfirm(value) {
      this.photoSize = value.value; // 更新实际值
      this.photoSizeText = value.text; // 更新显示文字
      this.showSizePicker = false;
      this.setPhotoDimensions(); // 更新证件照展示尺寸
    },
    onColorConfirm(value) {
      this.backgroundColor = value.value; // 更新实际值
      this.backgroundColorText = value.text; // 更新显示文字
      this.showColorPicker = false;
    },
    setPhotoDimensions() {
      if (this.photoSize === 'one-inch') {
        this.photoWidth = 295;
        this.photoHeight = 413;
      } else if (this.photoSize === 'two-inch') {
        this.photoWidth = 413;
        this.photoHeight = 626;
      } else if (this.photoSize === 'mini one-inch') {
        this.photoWidth = 260;
        this.photoHeight = 378;
      } else if (this.photoSize === 'mini two-inch') {
        this.photoWidth = 413;
        this.photoHeight = 531;
      } else if (this.photoSize === 'big one-inch') {
        this.photoWidth = 390;
        this.photoHeight = 567;
      } else if (this.photoSize === 'big two-inch') {
        this.photoWidth = 413;
        this.photoHeight = 626;
      } else if (this.photoSize === 'five-inch') {
        this.photoWidth = 1050;
        this.photoHeight = 1499;
      } else if (this.photoSize === 'techer-inch') {
        // 教师资格证
        this.photoWidth = 295;
        this.photoHeight = 413;
      } else if (this.photoSize === 'gongwuyuan') {
        // 国家公务员考试
        this.photoWidth = 295;
        this.photoHeight = 413;
      } else if (this.photoSize === 'chujikuaijishi') {
        // 初级会计考试
        this.photoWidth = 295;
        this.photoHeight = 413;
      } else if (this.photoSize === 'English-inch') {
        // 英语四六级考试
        this.photoWidth = 144;
        this.photoHeight = 192;
      } else if (this.photoSize === 'computer-inch') {
        // 计算机等级考试
        this.photoWidth = 390;
        this.photoHeight = 567;
      } else if (this.photoSize === 'yanjiusheng') {
        // 研究生考试
        this.photoWidth = 531;
        this.photoHeight = 709;
      } else if (this.photoSize === 'shebaoka') {
        // 社保卡
        this.photoWidth = 358;
        this.photoHeight = 441;
      } else if (this.photoSize === 'electronic-driving-license') {
        // 电子驾驶证
        this.photoWidth = 260;
        this.photoHeight = 378;
      } else if (this.photoSize === 'us-qianzheng') {
        // 美国签证
        this.photoWidth = 600;
        this.photoHeight = 600;
      } else if (this.photoSize === 'janpan-qianzheng') {
        // 日本签证
        this.photoWidth = 295;
        this.photoHeight = 413;
      } else if (this.photoSize === 'hanguo-qianzheng') {
        // 韩国签证
        this.photoWidth = 413;
        this.photoHeight = 531;
      }
    },
    afterRead(file) {
      file.status = 'uploading';
      file.message = '制作中...';
      const image = new Image();
      image.src = file.content;

      image.onload = () => {
        this.imageWidth = image.width;
        this.imageHeight = image.height;
        this.compressImage(file.file, 2.4)
          .then(base64File => {
            const base64Str = base64File.split(',')[1];
            this.generatePhoto(base64Str);
            file.status = 'done';
            file.message = '制作成功';
          })
          .catch(error => {
            console.error(error);
            file.status = 'failed';
            file.message = '压缩失败';
          });
      };
    },
    beforeRead(file) {
      if (!file.type.startsWith('image/')) {
        toast().tip('请选择图片');
        return false;
      }
      return true;
    },
    async compressImage(file, targetSize) {
      return new Promise(resolve => {
        const reader = new FileReader();

        reader.onload = event => {
          const image = new Image();
          image.src = event.target.result;

          image.onload = () => {
            let quality = 0.8;

            const compress = () => {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              const pixelRatio = window.devicePixelRatio || 1;

              canvas.width = image.width;
              canvas.height = image.height;

              ctx.drawImage(image, 0, 0, image.width, image.height);

              canvas.toBlob(
                blob => {
                  blobToBase64(blob, base64Str => {
                    let byteString;
                    if (base64Str.split(',')[0].indexOf('base64') >= 0) {
                      byteString = atob(base64Str.split(',')[1]);
                    } else {
                      byteString = decodeURIComponent(base64Str.split(',')[1]);
                    }
                    const byteLength = byteString.length;
                    if (
                      byteLength > targetSize * 1024 * 1024 &&
                      quality > 0.1
                    ) {
                      quality -= 0.1;
                      compress();
                    } else {
                      resolve(base64Str);
                    }
                  });
                },
                'image/jpeg',
                quality
              );
            };
            compress();
          };
        };

        reader.readAsDataURL(file);
      });
    },
    generatePhoto(base64Str) {
      this.setPhotoDimensions(); // 设置证件照展示尺寸
      this.loading = true;
      generateIdPhoto({
        input_image_base64: base64Str,
        height: this.photoHeight,
        width: this.photoWidth,
        color: this.backgroundColor,
        hd: this.isHighDefinition,
      })
        .then(data => {
          this.generatedPhotos = data;
        })
        .catch(err => {
          toast().tip(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    regeneratePhoto() {
      if (this.fileList.length > 0) {
        const file = this.fileList[0];
        this.afterRead(file);
      } else {
        toast().tip('请先上传照片');
      }
    },
    savePhoto(base64Str) {
      try {
        if (isInWeixin) {
          Toast(this.savePicTips);
          return;
        }
        if (isInJglh) {
          saveImageBase64(base64Str)
            .then(res => {
              toast().tip('图片保存成功');
            })
            .catch(() => {
              toast().tip('请使用最新版交广领航APP');
            });
          return;
        }
        const link = document.createElement('a');
        link.href = `${base64Str}`;
        link.download = 'user_photo.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.log('🚀 ~ savePhoto ~ error:', error);
      }
    },
    playPhotos(imgs, initIndex = 0) {
      const photos = imgs.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, '_xl'),
        };
      });
      console.log('图片++', photos);
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    onImgClick(e) {
      this.playPhotos([getImageURL(this.imageId)], 0);
    },
  },
  mounted() {
    this.setPhotoDimensions(); // 初始化证件照展示尺寸
    document.title = '交广领航AI生成证件照'; // 设置页面标题
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.van-button--primary {
  background: linear-gradient(90deg, #000085, #6f42c1); // 科技蓝渐变科技紫
  border: none; // 移除边框
  color: white; // 文字颜色为白色
  transition: background 0.3s ease; // 添加渐变过渡效果
}

.van-button--primary:hover {
  background: linear-gradient(90deg, #6f42c1, #000085); // 悬停时反转渐变方向
}

.field-row {
  padding: 20px;
  background-color: #f5f5f5;
  height: 100vh;
  overflow-y: auto;
}

.options-wrap {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;

    .van-field {
      flex: 1;
      margin-bottom: 0;
    }

    .switch-label {
      font-size: 14px;
      color: #333;
      margin-right: 10px;
    }
  }
}
.mt-15 {
  margin-top: 15px;
}
.upload-wrap {
  margin-top: 20px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .uploadImg {
    max-width: 80%;
    height: auto;
    border-radius: 8px;
    margin: 0 auto;
    display: block;
  }

  .upload-note {
    font-size: 12px;
    color: #999;
    margin-top: 10px;
    text-align: center;
  }

  .regenerate-button {
    width: 100%;
    margin-top: 15px;
  }
}

.generated-photos {
  margin-top: 20px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .photo-container {
    text-align: center;

    h3 {
      font-size: 18px;
      color: #333;
      margin-bottom: 15px;
    }

    .generated-image {
      border-radius: 8px;
      border: 1px solid #eee;
      margin-bottom: 15px;
      display: block;
      width: 100% !important;
      height: auto !important;
    }

    .van-button {
      width: 100%;

      border-radius: 8px;
      margin-bottom: 15px;
    }
  }

  .regenerate-button {
    width: 100%;
    border-radius: 8px;
    margin-top: 15px;
  }
}
</style>
