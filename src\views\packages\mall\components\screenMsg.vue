<template>
  <vue-seamless
    :data="list"
    :class-option="optionSingleHeight"
    :style="[{ transform: 'translateZ(0)' }]"
  >
    <div v-for="(item, index) in list" :key="index">
      <div class="fake-msg-item" ref="item">
        <!-- <biz-image class="user-icon" :src="item.userDetail.portrait || portraitDefault" type='?imageView2/0/format/jpg'></biz-image> -->
        <img
          :src="getUserPortraitUrl(item.userDetail.portrait)"
          class="user-icon"
        />
        <span
          >{{
            (item.userDetail
              ? item.userDetail.name || '车友1041'
              : ''
            ).substring(0, 1) +
            '***' +
            (item.userDetail ? item.userDetail.name || '车友1041' : '').substr(
              -1
            )
          }}
          , {{ getInfoMsgTime(item.payTime)
          }}{{ item.category == 'buy' ? '购买' : '浏览' }}了该商品</span
        >
      </div>
      <div class="row-gap"></div>
    </div>
  </vue-seamless>
</template>

<script>
// 组件用法可以参考 mall-> bargain -> bargain-detail.vue ； 组件样式在 mall -> assets -> styles ->FloatMsg.scss
import { getImageURL } from '@/common/image';

import vueSeamless from 'vue-seamless-scroll';
/**
 *  用于下单填写收货地址页面显示商品信息
 */
export default {
  name: 'BargainPop',
  props: {
    // 消息数据
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  components: {
    vueSeamless,
  },
  data() {
    return {
      singleHeight: 0,
      portraitDefault: require('@/assets/images/account/portrait-default.png'),
      // optionSingleHeight: {},
    };
  },
  computed: {
    optionSingleHeight() {
      return {
        limitMoveNum: 0,
        hoverStop: false,
        // singleHeight: this.singleHeight, // 开启无缝滚动的数据量
        singleHeight: 60, // 单步运动停止的高度(默认值 0 是无缝不停止的滚动)，direction 为 0|1 时生效。
        waitTime: 2000,
        // isSingleRemUnit: true, // 是否开启 rem 度量, 使用rem会有小数偏差，数据量多时，会导致滚动偏差。
        openTouch: false, // 移动端开启 touch 滑动
      };
    },
    baseFontSize() {
      return (
        parseInt(
          window.getComputedStyle(document.documentElement, null).fontSize
        ) || 1
      );
    },
    realSingleStopHeight() {
      return this.singleHeight * this.baseFontSize;
    },
  },
  watch: {
    // // 不启用
    // list(val) {
    //   this.setSingleHeight()
    // }
  },
  mounted() {
    // this.setSingleHeight()
  },
  methods: {
    // 计算飘屏下单了多长时间
    getInfoMsgTime(payTime) {
      let currentTime = parseInt((new Date().getTime() - payTime) / 1000);
      if (currentTime < 60) {
        // 一分钟以内
        return '刚刚';
      } else if (currentTime < 3600) {
        // 一小时前之内
        return Math.floor(currentTime / 60) + '分钟前';
      } else {
        return '1小时前';
      }
    },
    // 处理用户头像URL
    getUserPortraitUrl(portrait) {
      if (!portrait) {
        return this.portraitDefault;
      }
      // 使用getImageURL处理，添加七牛处理后缀
      return getImageURL(
        portrait,
        '?imageView2/1/w/100/h/100/format/webp/q/90'
      );
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
.fake-msg {
  position: absolute;
  top: 10%;
  left: 16px;
  // max-width: 60%;
  height: 30px;
  z-index: 2;
  overflow: hidden;
  .fake-msg-item {
    height: 20px;
    line-height: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 15px;
    padding: 5px 12px 5px 5px;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    // &::before{
    //   content: '';
    //   height: 20PX;
    //   display: block;
    //   width: 100%;
    //   background: black;
    //   margin-bottom: -25px;
    //   opacity: 0.7;
    //   border-radius: 8px;
    //   padding: 5px 12px 5px 5px;
    //   z-index: -1;
    // }
    .user-icon {
      width: 20px;
      height: 20px;
      border-radius: 8px;
      margin-right: 6px;
    }
    > span {
      // width: 0;
      flex: 1;
      @include singleline-ov();
    }
  }
  .row-gap {
    width: 40%;
    height: 30px;
  }
}
</style>
