import APIModel from '../APIModel';

const api = new APIModel({
  // 获取充电站列表
  '/charging-station/list': '/Radio/api/chargingstations/query',

  // 获取充电站详情
  '/charging-station/detail': '/Radio/api/chargingstations/{id}',
});

/**
 * 获取充电站列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，默认1
 * @param {number} params.size 每页数量，默认10
 * @param {number} params.lat 纬度
 * @param {number} params.lng 经度
 * @param {string} params.keyword 搜索关键词
 * @param {string} params.sortBy 排序方式：distance(距离)、rating(评分)、price(价格)
 * @param {Array} params.filters 筛选条件数组
 */
export function getChargingStationList(params) {
  const url = api.render('/charging-station/list');
  const data = Object.assign(
    // {
    //   pageNum: 1,
    //   pageSize: 10,
    //   sortBy: 'distance',
    // },
    params,

    {
      pageNum: params.page,
      pageSize: params.size,
    }
  );

  return api.postJSON(url, data);
}

/**
 * 获取充电站详情
 * @param {string} stationId 充电站ID
 */
export function getChargingStationDetail(stationId) {
  const url = api.render('/charging-station/detail', { id: stationId });

  return api.doGet(url, { stationId });
}

/**
 * 搜索充电站
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @param {number} params.lat 纬度
 * @param {number} params.lng 经度
 * @param {number} params.page 页码
 * @param {number} params.size 每页数量
 */
export function searchChargingStation(params) {
  const url = api.render('/charging-station/list');
  const data = Object.assign(
    params,

    {
      pageNum: params.page,
      pageSize: params.size,
    }
  );

  // 先尝试调用真实API，失败时使用静态数据
  return api.doGet(url, data);
}
