<template>
  <prize-pop-base :prize="prize" :prizeType="prizeType" :prizeTip="prizeTipText" v-on="$listeners">
    <template #content>
      <div class="prize-name">
        <span class="name">{{ prize.prizeName }}</span>
      </div>
    </template>
  </prize-pop-base>
</template>

<script>
import { PrizeType as PrizeTypeEnum } from '@/enums';
import PrizePopBase from '../PrizePopBase.vue';

export default {
  name: 'PrizePopNormal',
  components: {
    PrizePopBase
  },
  props: {
    prize: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prizeType: PrizeTypeEnum.NORMAL.valueOf()
    };
  },
  computed: {
    receiveDesc() {
      return '可在中奖记录中完善收货地址';
    },
    prizeTipText() {
      return '';
    }
  }
};
</script>

<style lang="scss" scoped>
.prize-name {
  font-size: 20px;
  color: #ff273b;
  text-align: center;
  margin-bottom: 12px;
}
</style>
