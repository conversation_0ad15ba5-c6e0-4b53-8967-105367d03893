<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header title="预约订单">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="service-cn" @click="showServiceHelp">
        <svg
          class="icon-service-cn"
          style="transform: scale(2.2)"
          aria-hidden="true"
        >
          <use xlink:href="#icon-service-cn"></use>
        </svg>
      </x-button>
    </x-header>
    <content-view
      class="inspection-wrap"
      ref="view"
      :status="status"
      :refreshAction="refreshAction"
      @refresh="refresh"
      @reload="reload"
      @scroll-bottom="loadMore"
    >
      <template v-if="status == AppStatus.READY">
        <div v-if="list && list.length > 0" class="reserve-list">
          <template>
            <div
              v-for="(item, index) in list"
              :key="index"
              class="reserve-item"
            >
              <div class="reserve-card">
                <div class="reserve-header">
                  <img
                    class="reserve-icon"
                    src="./images/biz/online-inspection.png"
                    alt="预约审车"
                  />
                  <span class="reserve-title">{{
                    item.categoryId == VehicleBusinessType.ONLINE_INSPECTION
                      ? '预约审车'
                      : '代驾审车'
                  }}</span>
                </div>
                <div class="reserve-content">
                  <div class="station-info">
                    <p class="station-name">{{ item.businessName || '' }}</p>
                    <p class="car-info">车辆信息：{{ item.carno || '' }}</p>
                    <div class="secretary-info" v-if="item.businessTelePhone">
                      <p class="secretary-label">小秘书电话：</p>
                      <div class="phone-list">
                        <div
                          v-for="(phone, phoneIndex) in getPhones(
                            item.businessTelePhone
                          )"
                          :key="phoneIndex"
                          class="phone-pill"
                          @click="handleCallPhone(phone)"
                        >
                          <van-icon name="phone-o" color="#1989fa" />
                          <span class="phone-number">{{ phone }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="reserve-action">
                    <button class="btn-pay" @click="handlePay(item)">
                      审车已完成去支付
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <!-- <div v-else class="empty-container">
            <div class="empty-tip">
              <img src="@/assets/images/empty.png" class="empty-img" />
              <p>暂无预约订单</p>
            </div>
          </div> -->
        </div>
        <list-loader
          v-if="list.length > 5"
          :options="$_loader_options"
          @load="loadMore"
        ></list-loader>
        <list-placeholder
          v-if="!list.length"
          icon="~@/assets/images/mall/empty.png"
          >没有记录</list-placeholder
        >
      </template>
    </content-view>
  </container>
</template>

<script>
import { AppStatus, OrderBizType } from '@/enums';
import { mixinLoader, mixinAuthRouter, mixinOrder } from '@/mixins';
import { dialog, toast, loading } from '@/bus';
import { callPhone } from '@/bridge';
import {
  getInspectionShopInfo,
  getReservationInspectionList,
} from '@pkg/vehicle-business/api';
import { Icon } from 'vant';

import { VehicleBusinessType } from '../enums';

const PAGE_SIZE = 10;
export default {
  name: 'VehicleOrdersReserve',
  components: {
    [Icon.name]: Icon,
  },
  mixins: [mixinLoader, mixinAuthRouter, mixinOrder],
  data() {
    return {
      AppStatus,
      VehicleBusinessType,
      status: AppStatus.LOADING,
      refreshAction: 1,
      cooperationChannel: this.$route.query.cooperationChannel,
      list: [],
      shop: {}, // 门店信息
    };
  },
  computed: {
    filterParams() {
      return {
        ...this.$_loader_params,
      };
    },
  },
  mounted() {},
  methods: {
    getPhones(phoneStr) {
      if (!phoneStr) return [];
      return phoneStr.split(',').filter(p => p && p.trim());
    },
    handleCallPhone(phone) {
      callPhone(phone);
    },
    showServiceHelp() {
      this.$router.push('/vehicle-business/help');
    },
    init() {
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList().then(
        res => {
          if (this.status != AppStatus.READY) {
            this.status = AppStatus.READY;
          }
          return res;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    getList(page = 1) {
      const params = { ...this.filterParams, page };
      return this.$_loader_bind(getReservationInspectionList, res => {
        if (page === 1) {
          this.list = res.list;
        } else {
          this.list = this.list.concat(res.list);
        }
        return res.list;
      }).load(params);
    },
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.filterParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },
    refreshQuietly() {
      const currentPage = this.$_loader_getPage();
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(this.list.length);
      return this.getList().then(
        res => {
          this.$_loader_setPage(currentPage);
          this.$_loader_setPageSize(PAGE_SIZE);
          return res;
        },
        err => {
          // this.status = AppStatus.ERROR;
          toast().tip('刷新失败，请重试！');
          console.error(err);
        }
      );
    },
    validateForm(order) {
      return new Promise((resolve, reject) => {
        if (!order.carno) {
          reject('车牌号码为空！');
        }
        if (
          !order.name &&
          order.categoryId != VehicleBusinessType.AGENT_INSPECTION
        ) {
          reject('车主姓名为空！');
        }
        if (!/^1\d{10}$/i.test(order.phone)) {
          reject('车主手机号码为空！');
        }
        if (!order.firstRegistDate) {
          reject('初次登记日期为空！');
        }
        resolve();
      });
    },
    getInspectionShopInfo(bid, categoryId) {
      return getInspectionShopInfo(bid, categoryId).then(res => {
        this.shop = res;
      });
    },
    async handlePay(order) {
      try {
        await this.getInspectionShopInfo(order.bid, order.categoryId);
        await this.validateForm(order);

        const bizType = {
          [VehicleBusinessType.ONLINE_INSPECTION_FOR_SPECIAL]:
            OrderBizType.VehicleInspectionForSpecial,
          [VehicleBusinessType.ONLINE_INSPECTION]:
            OrderBizType.VehicleInspectionReservation, // 预约上线审车
          [VehicleBusinessType.AGENT_INSPECTION]:
            OrderBizType.AgentVehicleInspectionReservation, // 预约代办审车
          [VehicleBusinessType.NEW_ENERGY_INSPECTION]:
            OrderBizType.VehicleInspection, // 新能源审车
          [VehicleBusinessType.VAN_INSPECTION]: OrderBizType.VehicleInspection, // 面包车审车
        };
        this.$_order_preSubmit(
          {
            type: bizType[order.categoryId],
            resultUrlRender(oid) {
              return `/vehicle-business/order/result/${oid}`;
            },

            form: {
              ...order,
              categoryId: order.categoryId,
            },
            // 审车订单查询优惠券信息参数
            ticketQuery: {
              category: this.$_order_TicketCategory.OnlineCarInspection,
              rid: order.bid,
            },
            // 合作渠道——微信社群营销
            cooperationChannel: this.$route.query.cooperationChannel,
            // 确认订单页面订单信息
            order: {
              title: this.shop.name,
              id: 0,
              name: '预约审车',
              price: this.shop.price,
              mprice: this.shop.price,
              vipPrice: this.shop.vipPrice || this.shop.price,
            },
          },
          'push'
        );
      } catch (err) {
        err &&
          dialog().alert(err, {
            title: '提示',
          });
      } finally {
        loading(false);
      }
    },
    reload() {
      this.init();
    },
    onLeave() {
      // this.$refs.orders.onLeave();
    },
    onResume() {
      this.refreshQuietly();
    },
    refresh() {
      this.init()
        .then(res => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip('刷新失败');
          this.refreshAction = Date.now();
        });
    },
  },
};
</script>

<style scoped>
.inspection-wrap {
  background-color: #f5f5f5;
}

.reserve-list {
  padding: 15px;
}

.reserve-item {
  margin-bottom: 10px;
}

.reserve-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reserve-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.reserve-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.reserve-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.reserve-content {
  padding: 15px;
}

.station-info {
  margin-bottom: 20px;
}

.station-name {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
}

.car-info {
  font-size: 14px;
  color: #666;
}

.secretary-info {
  margin-top: 10px;
}

.secretary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.phone-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.phone-pill {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  border-radius: 20px;
  padding: 6px 12px;
  border: 1px solid #e1efff;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1;
}

.phone-pill:hover {
  background-color: #e1efff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.phone-pill .van-icon-phone-o {
  color: #1989fa;
  font-size: 16px;
  margin-right: 3px;
}

.phone-number {
  color: #1989fa;
  font-size: 14px;
  font-weight: 500;
}

.reserve-action {
  text-align: center;
}

.btn-pay {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  background-color: #ff5722;
  color: white;
  font-size: 16px;
  border: none;
  outline: none;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #999;
  font-size: 14px;
}

.empty-img {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}
</style>
