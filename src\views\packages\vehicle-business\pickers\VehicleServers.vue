<template>
  <container @ready="init" @leave="onLeave" @resume="onResume">
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <content-view
      class="view-search"
      ref="view"
      :status="status"
      :refreshAction="refreshAction"
      @scroll-bottom="loadMore"
      @refresh="refresh"
      @reload="reload"
    >
      <!-- <template slot="head"  v-if="status == AppStatus.READY" >
          <x-select ref="select" v-model="filterOption" :options="options"></x-select>
        </template> -->
      <template v-if="status == AppStatus.READY">
        <div class="shop-box">
          <div class="shop-list">
            <template v-if="listStatus == AppStatus.LOADING">
              <div class="list-loading">正在查询...</div>
            </template>
            <template v-else-if="listStatus == AppStatus.ERROR">
              <div class="list-error">加载出错</div>
            </template>
            <template v-else-if="listStatus == AppStatus.READY">
              <list-item
                v-for="(item, index) in list"
                :key="index"
                :shop="item"
                @click.native="setItem(item)"
              >
              </list-item>
              <list-placeholder
                v-if="!list || !list.length"
                icon="~@/assets/images/ticket/empty.png"
                >暂未找到相关检测站</list-placeholder
              >
              <list-loader
                v-if="list.length"
                :options="$_loader_options"
                @load="loadMore"
              ></list-loader>
            </template>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

$border-color: rgb(242, 242, 242);
.list-item {
  background: white;
  @include border-bottom($border-color);
  .picture {
    width: 80px;
    height: 80px;
    margin-right: 5px;
  }
  padding: 5px;
  .list-item__content {
    flex: 1;
  }
  .rmb {
    color: #fe861f;
    font-size: 1.1em;
  }
}
.list-error,
.list-empty,
.list-loading {
  text-align: center;
  padding: 50px 0;
  color: gray;
}
</style>
<script>
import { getLocation } from '@/bridge';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { dialog, toast, back } from '@/bus';
import { getVehicleBusinessShops } from '@pkg/vehicle-business/api';
import { VehicleBusinessType } from '@pkg/vehicle-business/enums';
import { confirmBusinessServer } from '@pkg/vehicle-business/bus';
import ShopListItem from './_ShopListItem.vue';

const PAGE_SIZE = 10;
export default {
  name: 'VehicleServers',
  components: {
    'list-item': ShopListItem,
  },
  mixins: [mixinLoader, mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      listStatus: AppStatus.LOADING,
      mode: this.$route.params.mode, // VehicleBusinessType
      refreshAction: 1,
      selectedItem: null,
      filterOption: {
        type: '',
        brand: '',
        specifications: '',
      },
      page: {
        list: [],
      },
      options: {},
      optionsCache: {},
    };
  },
  computed: {
    pageTitle() {
      /* if (this.mode == VehicleBusinessType.CAR_REGISTER || this.mode == VehicleBusinessType.CAR_TRANSFER) {
        return '选择服务公司';
      } */
      return '选择服务公司';
    },
    list() {
      return this.page.list;
    },
    queryParams() {
      return {
        categoryId: this.mode,
        ...this.geo,
        ...this.$_loader_params,
      };
    },
  },
  methods: {
    getLocation() {
      return getLocation().then(data => {
        this.geo = {
          lng: data.longitude,
          lat: data.latitude,
        };
        return data;
      });
    },
    initPageData() {
      return this.getLocation().then(() => {
        this.$_loader_setPage(1);
        this.$_loader_setPageSize(1000); // 设置最大页码, 此接口没有分页，所以此处默认只加载一次
        this.getList(1)
          .then(res => {
            this.listStatus = AppStatus.READY;
            this.status = AppStatus.READY;
            return res;
          })
          .catch(e => {
            toast().tip(e);
            console.error(e);
            this.status = AppStatus.ERROR;
          });
      });
    },
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      this.getList(this.queryParams.page + 1).catch(e => {
        toast().tip(e);
        console.error(e);
      });
    },
    getList(page = 1) {
      const params = { ...this.queryParams, page };
      return this.$_loader_bind(getVehicleBusinessShops, res => {
        if (page === 1) {
          this.page.list = res;
        } else {
          this.page.list = this.page.list.concat(res);
        }
        return res;
      }).load(params);
    },
    init() {
      console.log('init...');
      this.$_loader_setPageSize(PAGE_SIZE);
      this.initPageData();
    },
    refresh() {
      this.initPageData()
        .then(res => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip(e);
          this.refreshAction = Date.now();
        });
    },
    onResume() {},
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    setItem(item) {
      confirmBusinessServer(item);
      this.$_router_back();
    },
  },
};
</script>
