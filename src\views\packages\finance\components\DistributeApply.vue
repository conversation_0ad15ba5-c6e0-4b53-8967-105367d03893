<template>
  <div class="lh-form">
    <h2 class="form-title">申请信息</h2>
    <van-form validate-first ref="form">
      <div class="field-row">
        <div class="label required">真实姓名</div>
        <van-field
          v-model="form.trueName"
          name="trueName"
          label=""
          autocomplete="off"
          :formatter="formatUsername"
          placeholder="请输入您的真实姓名"
          :rules="[{ required: true, message: '请输入您的真实姓名' }]"
        />
      </div>
      <div class="field-row">
        <div class="label required">联系方式</div>
        <van-field
          v-model="form.phone"
          name="phone"
          label=""
          type="tel"
          maxlength="11"
          readonly
          disabled
          autocomplete="off"
          :formatter="formatterTel"
          placeholder="请输入联系方式"
          :rules="[
            {
              required: true,
              pattern: patternTel,
              message: '请输入正确的联系方式',
            },
          ]"
        />
      </div>
      <div class="field-row">
        <div class="label required">身份证号码</div>
        <van-field
          v-model="form.cardId"
          name="cardId"
          label=""
          :maxlength="18"
          autocomplete="off"
          :formatter="formatterIDNumber"
          placeholder="请输入身份证号码"
          :rules="[
            {
              required: true,
              pattern: patternID,
              message: '请输入正确的身份证号码',
            },
          ]"
        />
      </div>
      <div class="field-row">
        <div class="label">申请理由(选填)</div>
        <div class="flex-wrap">
          <van-field
            v-model="form.content"
            name="content"
            rows="4"
            autosize
            show-word-limit
            autocomplete="off"
            label=""
            maxlength="200"
            type="textarea"
            placeholder="请填写"
          />
        </div>
      </div>

      <div class="agreement">
        <van-checkbox
          v-model="checked"
          shape="square"
          checked-color="#FD4925"
          icon-size="16px"
          >已阅读并同意</van-checkbox
        >
        <span @click="toAgreement">《交广领航团长卖手服务协议》</span>
      </div>
      <van-button
        class="submit-btn"
        round
        block
        type="danger"
        :loading="submitLoading"
        @click="submit"
        native-type="submit"
        >提交申请</van-button
      >
    </van-form>
  </div>
</template>
<script>
import { formatDate } from '@/utils';
import { dialog, loading } from '@/bus';
import { mixinAuthRouter } from '@/mixins';
import { mixinForm } from '@pkg/finance/mixins/form.js';
import { applyAdd, applyDetail } from '@pkg/finance/api';

import {
  Icon,
  Button,
  Dialog,
  Form,
  Field,
  Cell,
  RadioGroup,
  Radio,
  Toast,
  Checkbox,
} from 'vant';

export default {
  name: 'DistributeApply',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    confirmButtonText: {
      type: String,
      default: '',
    },
    cancelButtonText: {
      type: String,
      default: '',
    },
    popIcon: {
      type: String,
      default: require('../assets/images/pop_icon.png'),
    },
  },
  mixins: [mixinAuthRouter, mixinForm],
  components: {
    [Button.name]: Button,
    [Form.name]: Form,
    [Field.name]: Field,
    [Cell.name]: Cell,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Toast.name]: Toast,
    [Checkbox.name]: Checkbox,
    [Icon.name]: Icon,
  },
  data() {
    return {
      showPopup: this.show,
      form: {
        trueName: '',
        phone: '',
        cardId: '',
      },
      checked: false,
      submitLoading: false,
      // popIcon: require('../assets/images/pop_icon.png'),
    };
  },
  computed: {},
  watch: {
    show(val) {
      this.showPopup = val;
    },
  },
  mounted() {
    this.form.phone = this.$_auth_userInfo.phone;
  },
  methods: {
    ...{ formatDate },
    submit(params) {
      if (!this.checked) {
        this.$toast('请勾选服务协议');
        return;
      }
      this.$refs.form
        .validate()
        .then(res => {
          this.submitLoading = true;
          let formatParams = {
            trueName: this.form.trueName,
            phone: this.form.phone,
            cardId: this.form.cardId,
            // uid: ''
          };
          applyAdd(formatParams)
            .then(res => {
              this.$emit('changeStatus', 0);
              this.$emit('submit');
              this.submitLoading = false;
            })
            .catch(err => {
              this.submitLoading = false;
              dialog('提示').alert(err);
            });
        })
        .catch(e => {
          this.submitLoading = false;
          this.$toast(e[0].message);
        });
    },
    toAgreement() {
      this.$_router_pageTo('/distribute/agreement', {
        title: '交广领航团长卖手服务协议',
        titleBar: false,
        shareButton: false,
      });
    },
    emit(event) {
      this.$emit(event);
    },
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
@import '~@pkg/finance/assets/style/form.scss';

.form-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10px;
}
.agreement {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1;
  padding-top: 6px;
  .van-checkbox {
    display: inline-flex;
  }
  &::v-deep .van-checkbox__label {
    font-weight: 400;
    color: #333333;
    line-height: 1;
  }
  > span {
    color: $lh-2022-primary-color;
  }
}
.submit-btn {
  font-size: 18px;
}
</style>
