<template>
  <container
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
    mode="fullscreen"
  >
    <x-header
      ref="header"
      :title="title"
      :fullscreen-config="headerFullscreenConfig"
    >
      <x-button slot="left" type="back"></x-button>
    </x-header>
    <content-view
      class="inspection-wrap"
      ref="view"
      :status="status"
      @reload="reload"
      @scroll="onPageScroll"
    >
      <div class="inspection-content" v-if="status == AppStatus.READY">
        <Card title="预约须知">
          <div class="tip-list-wrap">
            <ul class="tip-list" style="padding-bottom: 0">
              <li>下单后到达检测站直接联系下单检测站小秘书核销并服务即可</li>
              <li>
                车辆是否通过，由车况决定，平台保证车辆走预约通道，审核结果以车管所系统为准
              </li>
              <li style="color: #fd4925" v-if="category == 102">
                面包车需座椅齐全，太阳膜需要撕掉
              </li>
            </ul>
          </div>
        </Card>

        <vip-banner
          class="vip-banner"
          :vip="$_auth_isCarVip"
          @click="$_route_vipPage('car')"
          fill="contain"
          :banners="[
            require('@pkg/vehicle-business/assets/images/vip0-banner.png'),
            require('@pkg/vehicle-business/assets/images/vip1-banner.png'),
          ]"
        ></vip-banner>

        <Card title="预约信息">
          <InspectionForm
            :type="VehicleBusinessType.ONLINE_INSPECTION"
            :cars="actions"
            @pop="hideBottom"
            @switchCar="showPopover = true"
            v-model="formData"
          ></InspectionForm>
        </Card>
        <div class="cost-row">
          <div class="row">
            <span class="content-attr">审车费用</span>
            <div>
              <span class="rmb">{{ price }}</span>
            </div>
          </div>
          <div
            v-if="category == VehicleBusinessType.ONLINE_INSPECTION_FOR_CCB"
            class="row row-tip"
            style="color: red"
          >
            提醒：本活动不支持退款，请提前确认您的车辆需上线检测再进行下单
          </div>
        </div>

        <div class="button-sp-area">
          <div v-if="reserveWarn" class="reserve-tip">
            {{ formatDate(form2.appointmentTime, 'yyyy-MM-dd') }} 是
            <b>{{ reserveWarn }}</b
            >，请预约其他时间
          </div>
          <div v-else-if="form2.appointmentTime && counts" class="reserve-tip">
            {{
              formatDate(form2.appointmentTime, 'yyyy-MM-dd HH:mm')
            }}，今日此时间点之前有 <b>{{ counts }}</b
            >人预约
          </div>
          <!-- <van-button class="item-bar__button" block round :loading="loading" loading-text="提交中" type="danger"
            @click="submit">提交</van-button> -->
          <van-button
            class="item-bar__button"
            block
            round
            :loading="loading"
            loading-text="提交中"
            type="danger"
            @click="submit"
          >
            <div class="submit-text-wrap">
              <p>先审后付</p>
              <span>审车后再付款 立即预约</span>
            </div>
          </van-button>
        </div>
        <van-action-sheet
          v-model="showPopover"
          :actions="actions"
          cancel-text="取消"
          description="选择您的车辆"
          close-on-click-action
          @select="onSelect"
          @cancel="showPopover = false"
        />
      </div>
    </content-view>
  </container>
</template>

<script>
import { formatDate, fixRichHtmlImageSize, formatShopHours } from '@/utils';
import { AppStatus, OrderBizType } from '@/enums';
import FormValidater from '@/model/FormValidater';
import { mixinAuthRouter, mixinOrder } from '@/mixins';
import { getImageURL } from '@/common/image';
import { dialog, loading, toast } from '@/bus';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import { checkInspectionDay } from '@pkg/vehicle-business/utils/holiday';
import {
  getInspectionShopInfo,
  getInspectionReserveCount,
  createOrder4CCBOnlineVehicleInspection,
  createOrder4OnlineVehicleInspectionV2,
  getCarLicenseList,
  saveReservationInspection,
} from '@pkg/vehicle-business/api';
import { VehicleBusinessType } from '../enums';

import VipBanner from '@pkg/vehicle-business/components/_VipBanner.vue';
import Card from '@pkg/vehicle-business/components/Card.vue';
import InspectionForm from '@pkg/vehicle-business/components/InspectionForm.vue';
import { Button, ActionSheet, Dialog } from 'vant';
// 车辆类型
const CarType = {
  PERSONAL: 1, // 个人车辆
  COMPANY: 2, // 单位车辆
};

/**
 * BizCategory.Special: 审车专场
 * 核心需求：
  1、选定一家且唯一一家审车商家；
  2、允许一个用户多次下单；
  3、系统自提交交用户电话、用户名信息，其他信息为空；
  4、订单有效期为18个月；
  5、支持业务+会员组合支付；
  6、不支持退款。
  2020年6月7日11:54:15：专场功能暂不使用了，新需求借用专场下单接口，用户正常填写审车预约信息
 */
const BizCategory = {
  Normal: 1,
  NewEnergy: 101,
  Van: 102,
  CCB: 'ccb', // 建行专区
  LongPay: 'longpay', // 龙支付
  Special: '11', // 审车专场
};

function getShopCategory(c) {
  const Category = {
    [BizCategory.Normal]: VehicleBusinessType.ONLINE_INSPECTION,
    [BizCategory.NewEnergy]: VehicleBusinessType.NEW_ENERGY_INSPECTION,
    [BizCategory.Van]: VehicleBusinessType.VAN_INSPECTION,
    [BizCategory.CCB]: VehicleBusinessType.ONLINE_INSPECTION_FOR_CCB, // 建行专区
    [BizCategory.LongPay]: VehicleBusinessType.ONLINE_INSPECTION_FOR_CCBLONGPAY, // 建行龙支付专区
    [BizCategory.Special]: VehicleBusinessType.ONLINE_INSPECTION_FOR_SPECIAL, // 审车专场
  };
  return Category[c];
}

function getDefaultTime() {
  const now = new Date();
  const hour = now.getHours() + 1 > 18 ? 10 : now.getHours() + 1;
  const date = now.getHours() + 1 > 18 ? now.getDate() + 1 : now.getDate();
  const dateTime = new Date(
    now.getFullYear(),
    now.getMonth(),
    date,
    hour,
    0,
    0
  );
  return dateTime;
}

async function getPageData(shopId) {
  const shop = await getInspectionShopInfo(shopId);
  return Promise.resolve(shop);
}

export default {
  name: 'OnlineInspectionReserve',
  components: {
    VipBanner,
    Card,
    InspectionForm,
    [Button.name]: Button,
    [ActionSheet.name]: ActionSheet,
    [Dialog.name]: Dialog,
  },
  mixins: [mixinAuthRouter, mixinOrder],

  data() {
    const now = new Date();
    return {
      AppStatus,
      OrderBizType,
      CarType,
      keepAlive: false,
      VehicleBusinessType,
      status: AppStatus.LOADING,
      shop: {},
      counts: 0,
      category: getShopCategory(
        this.$route.query.category || this.$route.params.catId
      ),
      title: this.$route.params.title ? this.$route.params.title : '预约审车',
      reserveWarn: null,
      form: {
        agree: false,
        carType: CarType.PERSONAL,
        inspector: '',
        name: '',
        phone: '',
        number: '',
        // id_number: '',
        date: now.getTime() + 1000 * 60 * 60 * 24,
        time: getDefaultTime(),
      },
      loading: false,
      formData: {},
      showPopover: false,
      // 通过 actions 属性来定义菜单选项
      actions: [],
    };
  },
  mounted() {
    this.$_auth_checkSession(true);
    // dialog().alert(`
    //       温馨提示: 系统升级维护，暂停下单。
    //       `, {
    //   title: '通知'
    // });
  },
  computed: {
    // 全屏模式下 header配置
    headerFullscreenConfig() {
      return {
        // backgroundColor: 'rgba(56, 142, 253, 0)',
        backgroundColor: 'rgba(255, 255, 255, 0)',
        fullscreenHeight: window.innerWidth,
        scrolled: this.scrollTop,
      };
    },
    price() {
      if (this.$_auth_isCarVip) return this.shop.vipPrice;
      return this.shop.price;
    },
    // 商家营业时间，字段值为特殊格式，需要转换
    serviceTime() {
      const shop = this.shop;
      const start = formatShopHours(shop.serviceTimeS);
      const end = formatShopHours(shop.serviceTimeE);
      return {
        start,
        end,
      };
    },
    couldUseVipPrice() {
      return (
        this.$_auth_isCarVip &&
        this.shop.vipPrice > 0 &&
        this.shop.price > this.shop.vipPrice
      );
    },
    // 审车预约时间，最早只能预约1小时以后的时间
    selectableTime() {
      // const start = formatDate(Date.now() + 1000 * 60 * 60 * 1, 'HH:mm');
      return {
        start: this.serviceTime.start,
        end: this.serviceTime.end,
      };
    },
    form2() {
      return {
        bid: this.shop.id,
        name: this.formData.name,
        phone: this.formData.phone,
        address: this.shop.address,
        // appointmentTime: date.getTime(),
        carno: this.formData.carno,
        categoryId: this.category,
        carType: this.formData.carType,
        firstRegistDate: this.formData.firstRegistDate,
      };
    },
    formValidation() {
      if (!this.formData.name || !this.formData.carno || !this.formData.phone) {
        return false;
      }
      return true;
    },
  },
  watch: {
    ['form2.appointmentTime'](val, oldVal) {
      this.checkReserveCount();
    },
  },
  methods: {
    ...{ formatDate },
    onSelect(val) {
      let car = { ...val };
      let year = car.registDate.substring(0, 4);
      let month = car.registDate.substring(4, 6);
      let day = car.registDate.substring(6, 8);
      if (year && month && day) {
        car.registDate = `${year}-${month}-${day}`;
      } else {
        car.registDate = '';
      }
      this.formData = {
        ...this.formData,
        name: car.owner, // 车主姓名
        carno: car.carNo, // 车牌号
        firstRegistDate: car.registDate, // 初次登记日期
      };
    },
    hideBottom(flag) {
      this.showBottom = flag;
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      // this.status = AppStatus.READY;
    },
    getPageData() {
      const shopId = this.$route.params.id;
      Promise.all([
        getInspectionShopInfo(shopId, this.category),
        getCarLicenseList(),
      ]).then(
        ([shop, license]) => {
          if (!(shop.images instanceof Array) && shop.images) {
            shop.images = shop.images.split(',');
          }
          this.shop = shop;
          // 若有车牌号，自动填充车牌号
          const car = this.$route.query.car;
          if (car) {
            this.formData.carno = car;
          }
          if (
            this.$_auth_isLoggedIn &&
            this.category == VehicleBusinessType.ONLINE_INSPECTION_FOR_SPECIAL
          ) {
            this.formData.phone = this.$_auth_userInfo.phone;
          }
          if (license && license.length == 1) {
            let storedCar = { ...license[0] };
            let year = storedCar.registDate.substring(0, 4);
            let month = storedCar.registDate.substring(4, 6);
            let day = storedCar.registDate.substring(6, 8);
            if (year && month && day) {
              storedCar.registDate = `${year}-${month}-${day}`;
            } else {
              storedCar.registDate = '';
            }
            this.formData = {
              ...this.formData,
              name: storedCar.owner, // 车主姓名
              carno: storedCar.carNo, // 车牌号
              firstRegistDate: storedCar.registDate, // 初次登记日期
            };
          }
          if (license && license.length > 1) {
            this.actions = license.map(item => {
              return {
                ...item,
                name: item.carNo,
              };
            });
            setTimeout(() => {
              this.showPopover = true;
            }, 1000);
          }
          // this.checkReserveCount();
          this.status = AppStatus.READY;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    init() {
      this.getPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.getPageData();
    },
    validateForm(form) {
      return new Promise((resolve, reject) => {
        if (!this.formData.carno) {
          reject('请填写车牌号码！');
        }
        if (!this.formData.name) {
          reject('请填写姓名！');
        }
        if (!/^1\d{10}$/i.test(this.formData.phone)) {
          reject('请填写正确的手机号码！');
        }
        if (!this.formData.firstRegistDate) {
          reject('请选择初次登记日期！');
        }
        resolve();
      });
    },
    async submitReservation() {
      try {
        await this.validateForm(this.form2);
        const form = this.form2;
        // 添加合作渠道字段
        if (this.$route.query.cooperationChannel) {
          form.cooperationChannel = this.$route.query.cooperationChannel;
        }
        loading(true, '正在提交...');
        this.loading = true;
        saveReservationInspection(form).then(res => {
          loading(false);
          this.loading = false;
          Dialog.alert({
            title: '预约成功',
            message: '车务小秘书会及时联系您确认相关信息，请保持手机畅通！',
            theme: 'round-button',
          }).then(() => {
            // on close
            let query = {};
            if (this.$route.query.cooperationChannel) {
              query.cooperationChannel = this.$route.query.cooperationChannel;
            }
            this.$_router_replace({ name: 'VehicleOrderReserve', query });
          });
        });
      } catch (err) {
        this.loading = false;
        loading(false);
        err &&
          dialog().alert(err, {
            title: '提示',
          });
      }
    },
    submit() {
      // if (this.checkTimeTemp()) return;
      const that = this;
      if (this.reserveWarn) {
        const tip = `您确定想要在 <b style="color:red">${this.reserveWarn}</b> 审车吗？`;
        dialog().confirm(tip, {
          title: '提示',
          okText: '我确定',
          ok() {
            // that.submit1();
            that.submitReservation();
          },
        });
        return;
      }
      // that.submit1();
      that.submitReservation();
    },
    createLongPayOrder(form) {
      loading(true, '正在提交...');
      createOrder4OnlineVehicleInspectionV2({
        ...form,
        categoryId: this.category,
      })
        .then(res => {
          loading(false);
          const { oid, sysOid } = res;
          const orderType = OrderBizType.VehicleInspection;
          const nextPath = `/vehicle-business/order/result/${oid}`;
          // 新收银台，专区活动相关功能，仍然需要使用业务订单id，暂时通过将系统订单id和业务订单id串联传参
          const paymentInfo = {
            soid: `${sysOid}-${oid}`,
            type: orderType,
            method: 'replace',
            nextPath: nextPath,
          };
          this.$_route_cashierCheckout(paymentInfo);
        })
        .catch(err => {
          loading(false);
          err && dialog('提示').alert(err);
        });
    },
    createCCBOrder(form) {
      loading(true, '正在提交...');
      createOrder4CCBOnlineVehicleInspection(form)
        .then(res => {
          loading(false);
          const { oid, sysOid } = res;
          const orderType = OrderBizType.VehicleInspection;
          const nextPath = `/vehicle-business/order/result/${oid}`;
          // 新收银台，专区活动相关功能，仍然需要使用业务订单id，暂时通过将系统订单id和业务订单id串联传参
          const paymentInfo = {
            soid: `${sysOid}-${oid}`,
            type: orderType,
            // method: 'replace',
            nextPath: nextPath,
          };
          this.$_route_cashierCheckout(paymentInfo);
        })
        .catch(err => {
          loading(false);
          err && dialog('提示').alert(err);
        });
    },
    async submit1() {
      try {
        await this.validateForm(this.form2);
        const form = this.form2;
        // 建行专属审车使用走特殊通道
        if (this.category === VehicleBusinessType.ONLINE_INSPECTION_FOR_CCB) {
          this.createCCBOrder(form);
          return;
        }
        // 建行龙支付专属审车走特殊通道
        if (
          this.category === VehicleBusinessType.ONLINE_INSPECTION_FOR_CCBLONGPAY
        ) {
          this.createLongPayOrder(form);
          return;
        }

        const bizType = {
          [VehicleBusinessType.ONLINE_INSPECTION_FOR_SPECIAL]:
            OrderBizType.VehicleInspectionForSpecial,
          [VehicleBusinessType.ONLINE_INSPECTION]:
            OrderBizType.VehicleInspection, // 审车
          [VehicleBusinessType.NEW_ENERGY_INSPECTION]:
            OrderBizType.VehicleInspection, // 新能源审车
          [VehicleBusinessType.VAN_INSPECTION]: OrderBizType.VehicleInspection, // 面包车审车
        };
        this.$_order_preSubmit(
          {
            type: bizType[this.category],
            resultUrlRender(oid) {
              return `/vehicle-business/order/result/${oid}`;
            },

            form: {
              ...form,
              categoryId: this.category,
            },
            // 审车订单查询优惠券信息参数
            ticketQuery: {
              category: this.$_order_TicketCategory.OnlineCarInspection,
              rid: form.bid,
            },
            // 合作渠道——微信社群营销
            cooperationChannel: this.$route.query.cooperationChannel,
            // 确认订单页面订单信息
            order: {
              title: this.shop.name,
              id: 0,
              name: '预约审车',
              price: this.shop.price,
              mprice: this.shop.price,
              vipPrice: this.shop.vipPrice || this.shop.price,
            },
          },
          'replace'
        );
      } catch (err) {
        err &&
          dialog().alert(err, {
            title: '提示',
          });
      } finally {
        loading(false);
      }
    },
    // 查询选中时间点之前在当前内的尚未检车的预约人数 已取消预约时间选择
    checkReserveCount() {
      // const shop = this.form2.bid;
      // 根据sentry异常日志，某些终端首次调用此方法时，this.from2.bid(computed属性)无数据，可能computed计算结果在某些终端有一定延迟，故从$route中取shopId
      const shop = this.$route.params.id;
      const time = this.form2.appointmentTime;
      const result = checkInspectionDay(time);
      this.reserveWarn = result;
      console.warn(result);

      getInspectionReserveCount(shop, time)
        .then(res => {
          this.counts = res;
        })
        .catch(e => {
          console.error(e);
          toast().tip(e);
        });
    },
    checkTimeTemp() {
      const { available, message } = checkServiceStatus(
        ServiceEnum.CAR_INSPECTION
      );
      if (!available) {
        dialog().alert(message, {
          title: '通知',
        });
      }
      return !available;
    },
    onPageScroll(top) {
      this.scrollTop = top;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
@import '@pkg/vehicle-business/assets/styles/common.scss';

.inspection-wrap ::v-deep {
  background-image: url(~@pkg/vehicle-business/assets/images/page-head-small.png);
  background-size: 100% 100px;

  .scroller {
    padding-top: 60px;
  }

  .inspection-content {
    padding-bottom: 74px;
  }
}

.cost-row {
  background: #ffffff;
  border-radius: 10px;
  padding: 10px 15px;
  margin-bottom: 15px;

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .row-tip {
    line-height: 1.5;
  }

  .content-attr {
    font-size: 16px;
    color: #111111;
  }

  .rmb {
    font-size: 15px;
    font-weight: bold;
    color: #fd4925;

    &::before {
      font-size: 10px !important;
    }
  }
}

.item-bar__button {
  font-size: 18px;
  flex: 0 0 auto;
  height: 54px;
  // background: linear-gradient(-90deg, #fe7e4c, #ffa35f);
}

.submit-text-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    font-weight: bold;
    font-size: 15px;
    color: #ffffff;
  }

  span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.vip-banner {
  height: 40px;
  margin: 0 0 10px 0;
}

.button-sp-area {
  padding: 0 15px 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
}
</style>
