@import '~@/styles/mixin/index.scss';
$highlight-color: #f29e5c;
.order-detail-offline ::v-deep {
  padding: 15px;
  box-sizing: border-box;
  // padding-bottom: 50px;
  .weui-label {
    width: auto;
    margin-right: 10px;
  }
  .rmb {
    margin: 0;
  }
  .panel {
    padding: 5px;
  }
  .cell-tip {
    font-size: 13px;
    color: #f5222d;
    line-height: 1.2;
    i {
      font-style: normal;
    }
  }
  /* 审车结果 */
  .inspection-result {
    color: rgb(247, 6, 6);
  }
  .inspection-desc {
    font-size: 0.8em;
  }
  .inspection-valid,
  .inspection-invalid {
    background: 98% 5px no-repeat white;
    background-size: 50px;
  }
  .inspection-valid {
    background-image: url(~@pkg/vehicle-business/order/images/icons/valid.png);
  }
  .inspection-invalid {
    background-image: url(~@pkg/vehicle-business/order/images/icons/invalid.png);
  }

  // .order-panel {
  //   .panel-title {
  //     border-bottom: 0;
  //   }
  //   .weui-cell {
  //     border-top: 0;
  //     padding: 5px 10px;
  //     align-items: flex-start;
  //     &:last-child {
  //       border-bottom: 0;
  //     }
  //   }
  // }
  .panel {
    margin: 12px 0;
  }
  .panel-title {
    font-weight: bold;
    padding: 0;
    line-height: 20px;
    color: #333333;
    border: none;
  }
  .panel-content {
    margin-top: 15px;
  }
  .order-panel {
    border-radius: 10px;
    padding: 16px 15px;
    box-sizing: border-box;
    .weui-cell {
      border-top: 0;
      padding: 0;
      align-items: flex-start;
      margin-bottom: 15px;
      &:last-child {
        border-bottom: 0;
      }
    }
    .panel-content .weui-cell:last-child {
      margin-bottom: 0;
    }
  }
  // .order-info {
  //   .weui-label {
  //     width: auto;
  //   }
  //   .rmb-highlight {
  //     color: #f29e5c;
  //   }
  //   .weui-cell {
  //     padding-top: 8px;
  //     padding-bottom: 8px;
  //   }
  // }
  .order-pay-info,
  .order-info,
  .order-refund-info {
    // .weui-cell {
    //   padding-top: 3px;
    //   padding-bottom: 3px;
    // }
    .weui-label {
      width: auto;
      margin-right: 13px;
      font-size: 13px;
      font-weight: 500;
      color: #999999;
      line-height: 20px;
      &::after {
        content: ':';
        padding: 0 3px;
      }
    }
    .align-left,
    .align-right {
      font-size: 13px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;
    }

    .rmb-c {
      color: #fd4925;
    }
  }
  .order-status {
    color: #ef6c00;
  }
  .refund-tip {
    color: gray;
    font-size: 0.8em;
  }
  .order-comment {
    background: white;
    padding: 10px;
    margin: 12px 0;
    border: 1px solid #eaeaea;
    border-width: 1px 0;
    &::after {
      content: '\E605';
      font-family: iconfont;
      // float: right;
      font-size: 14px;
      margin-left: 10px;
      color: #bdbdbd;
    }
  }
  .shop {
    padding: 0;
    align-items: center;
    .shop-title {
      font-size: 15px;
      color: #111111;
    }
    .shop-body {
      font-size: 13px;
      color: #999999;
    }
  }
  .shop-logo {
    margin: 0;
  }
  .shop-phone {
    width: 68px;
    text-align: center;
    border-left: 1px solid #efefef;
    padding: 5px 10px;
    overflow: hidden;
    font-size: 14px;
    color: gray;
    box-sizing: border-box;
    &:active {
      background: #e0e0e0;
    }
    &::before {
      font-family: iconfont;
      display: block;
      content: '\e618';
      font-size: 22px;
      color: $lh-2022-primary-color;
    }
  }

  .btn-area {
    width: 100%;
    // height: 50px;
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // padding: 8px 15px;
    // box-sizing: border-box;
    // background: #fff;
    // z-index: 22;
    padding: 0;
    .weui-btn {
      width: 100%;
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      text-align: center;
      border-radius: 20px;
      font-size: 16px;
    }
    .weui-btn_primary {
      background: $lh-2022-primary-color;
      border: 1px solid $lh-2022-primary-color;
    }
    .weui-btn_primary:not(.weui-btn_disabled):active {
      background-color: $lh-2022-primary-color;
      opacity: 0.8;
    }
  }
  .btns {
    padding-top: 12px;
    display: flex;
    justify-content: flex-end;
    .custom-btn {
      border-radius: 27px;
      border: 1px solid #666666;
      padding: 0 12px;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      color: #333333;
    }
  }
  .custom-tips {
    font-size: 12px;
    color: #999999;
    line-height: 20px;
    margin-top: 10px;
  }
}
