<template>
  <van-cell-group inset>
    <van-cell :title="value.name" class="title-cell" :border="false" />
    <div class="help-grid">
      <div class="help-item" v-for="item in list" :key="item.id">
        <span class="name">{{ item.name }}</span>
        <span class="number" @click="call(item.phone)">{{ item.phone }}</span>
      </div>
    </div>
  </van-cell-group>
</template>

<script>
import { mapActions } from 'vuex';
import { AppStatus } from '@/enums';
import { dialog } from '@/bus';
import { callPhone } from '@/bridge';
import { Cell, CellGroup, Col, Row } from 'vant';

export default {
  name: 'ContractCard',
  props: {
    value: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Col.name]: Col,
    [Row.name]: Row,
  },
  data() {
    return {
      AppStatus,
      map: null,
      status: AppStatus.LOADING,
      error: '',
    };
  },
  watch: {},
  mounted() {},
  computed: {
    list() {
      return this.value.itemList || [];
    },
  },
  methods: {
    call(phone) {
      callPhone(phone);
    },
  },
};
</script>
<style lang="scss" scope>
.van-cell-group {
  border-radius: 8px;
  overflow: hidden;
  margin: 0;
  margin-bottom: 5px;
  background: #ffffff;
  padding: 0 15px 15px;
  line-height: 1;
  /* 标题栏 */
  .title-cell {
    font-weight: bold;
    padding: 15px 0;
    margin: 0;
    .van-cell__title span {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
}

/* 网格布局样式 */
.help-grid {
  padding: 0 5px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

/* 单个求助项,清除浮动 */
.help-item {
  width: 50%;
  box-sizing: border-box;
  // display: flex;
  padding: 10px 0;
  font-weight: 500;
  font-size: 14px;
  color: #333333;

  &:nth-child(2n) {
    padding-left: 5px;
  }
  &:nth-child(2n + 1) {
    padding-right: 5px;
  }
  span {
    // width: 50%;
    max-width: 60%;
    word-break: break-all;
    box-sizing: border-box;
  }
  .name {
    flex-grow: 0;
    float: left;
  }
  /* 号码样式 */
  .number {
    float: right;
    color: red;
    font-weight: bold;
    text-align: right;
  }
  &:after {
    content: '';
    display: block;
    clear: both;
  }
}
.help-grid-blue {
  .number {
    color: #1890ff;
  }
}
</style>
