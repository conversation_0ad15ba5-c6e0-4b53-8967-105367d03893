// import { api.doGet, doPost } from '../request/';
// import APIs from '../apis';
import APIModel from '../APIModel';

/**
 * 接口文档地址
 * http://jgrm.net:10230/swagger-ui.html#!/membership45new45controller/exchangeMembersJGYHUsingPOST
 */
const api = new APIModel({
  '/vip/interests': '/Membership-new-app/list/member/interests', // 会员权益信息
  '/vip/packages': '/Membership-new-app/list/membership', // 会员套餐信息
  // '/vip/buy': '/Membership-new-app/save/member/order', // 提交购买或续期会员订单（不支持红包，优惠券
  '/vip/buy': '/Membership-new-app/member/order/create', // 提交购买或续期会员订单(支持红包，优惠券
  '/vip/invite/config': '/Membership-new-app/get/member/invitationCode', // 获取邀请配置信息
  '/vip/invite/records': '/Membership-new-app/list/returnamount/uid', // 获取邀请返现记录
  '/vip/invite/income': '/Membership-new-app/count/returnamount', // 邀请返现收入总额
  '/vip/latest/buyers': '/Membership-new-app/list/member/carousel', // 最近购买的会员列表
  '/vip/exchange': '/Membership-new-app/exchange/members', // 兑换会员接口
  '/vip/exchange/check': '/Membership-new-app/check/conversionCode/type', // 查询兑换码兑换类型
  '/vip/exchange/jgyh': '/Membership-new-app/exchange/members/jgyh', // 交广银河兑换码兑换
})

/**
 * 获取vip会员可享受的权益信息
 */
export function getVipInterests() {
  const url = api.render('/vip/interests');
  return api.doGet(url);
}

/**
 * 获取最近购买会员的用户列表
 */
export function getLatestBuyers(rows) {
  const url = api.render('/vip/latest/buyers');
  return api.doGet(url, { row: rows });
}

/**
 * 获取vip会员套餐列表
 */
export function getVipPackages() {
  const url = api.render('/vip/packages');
  return api.doGet(url);
}

/**
 * 创建购买或续期VIP订单
 * @param {object} params
 * @param {string} params.id // 套餐id
 * @param {number} params.buyType // 购买类型
 * @param {string} params.buyChannel // 购买渠道
 * @param {string} params.invitationCode: // 邀请码
 * @param {object} extra 优惠券相关额外参数，详见 submitOrder
 */
export function createVipOrder(params, extra) {
  const url = api.render('/vip/buy');
  const data = {
    ...params,
    ...extra,
  }
  return api.doPost(url, data);
}

/**
 * 兑换vip会员
 * @param {string} code 兑换码
 */
export function exchangeVipByCode(code) {
  const url = api.render('/vip/exchange');
  return api.doPost(url, {
    conversionCode: code
  });
}

/**
 * 兑换交广银合vip会员
 * @param {string} code 兑换码
 * @param {string} carId 车牌号
 */
export function exchangeVipByCodeOfJgyh(conversionCode, carId) {
  const url = api.render('/vip/exchange/jgyh');
  return api.doPost(url, {
    conversionCode,
    carId,
  });
}

/**
 * 获取vip会员队兑换码信息
 */
export function getVipCodeType(conversionCode) {
  const url = api.render('/vip/exchange/check');
  return api.doGet(url, { conversionCode });
}

/**
 * 获取vip会员分享返现配置信息
 */
export function getVipInviteConfig() {
  const url = api.render('/vip/invite/config');
  return api.doGet(url);
}

/**
 * 获取vip会员分享邀请返现收入
 */
export function getVipInviteIncome() {
  const url = api.render('/vip/invite/income');
  return api.doGet(url);
}

/**
 * 获取vip会员分享邀请返现记录
 */
export function getVipInviteRecords(options) {
  const url = api.render('/vip/invite/records');
  return api.doGet(url, options);
}
