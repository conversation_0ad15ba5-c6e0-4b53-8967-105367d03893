<template>
  <page class="profile-home" @ready="init" @resume="onResume">
    <x-header :title="pageTitle" class="purple-header">
      <x-button slot="left" type="back"></x-button>
      <div slot="right">
        <x-button type="share" @click="share('show')"></x-button>
      </div>
    </x-header>
    <loading v-if="status == AppStatus.LOADING"></loading>
    <!-- <page-content
      ref="content"
      :status="status"
      :error="error"
      @reload="init"
    > -->
      <div v-else-if="status == AppStatus.READY" class="audio-wrap">
        <div class="content">
          <div class="radio-profile">
            <div class="card">
              <biz-image class="avatar" :src="radioInfo.image" :lazy="true">
              </biz-image>
              <div class="info">
                <p>{{ radioInfo.title }}</p>
                <span v-if="radioInfo.dj">主持人</span>
                <span v-if="radioInfo.dj">{{ radioInfo.dj }}</span>
              </div>
            </div>
            <div class="radio-info" :class="{ explanded: !showPlayList }">
              <p v-html="radioInfo.introduction" ref="introduction"></p>
              <div class="expland-btn" @click="showPlayList = !showPlayList">
                <span>{{ showPlayList ? "展开简介" : "点击收起" }}</span>
                <svg class="icon-svg">
                  <use xlink:href="#icon-xf-shouqi-wangshang"></use>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <!-- 播放列表 -->
        <van-popup
          v-model="showPlayList"
          :overlay="false"
          round
          position="bottom"
          class="playlist-pop"
          :class="{'show-mini-player': !fullScreen && currentSong.id}"
        >
          <play-list @select="selectPlay"></play-list>
        </van-popup>
      </div>
    <!-- </page-content> -->

    <page-loader
      v-else
      :status="status"
      :error="error"
      @reload="init"
    >
    </page-loader>
  </page>
</template>
<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { toast, back } from '@/bus'
import { isInJglh, isInWeixin, isInWeApp } from '@/common/env';
import { getImageURL } from '@/common/image';
import { getAppURL, getHTMLTextContent } from '@/utils';
import { AppStatus } from '@/enums'
import { addWebViewListener } from '@/bridge'
import { mapGetters, mapMutations, mapActions } from 'vuex';
import PlayList from '../components/PlayList.vue';
import { Popup } from 'vant';
import { getAudioProfile, getAudioList } from '../api';
const DEFAULT_COVER = require('../assets/images/album.png');
export default {
  name: 'Profile',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Popup.name]: Popup,
    PlayList
  },
  data() {
    return {
      isInWeApp,
      AppStatus,
      status: AppStatus.LOADING,
      error: '',
      showPlayList: true,
      audioList: [],
      expland: false,
      $_share_info: {
        path: '/audio/share/album',
        title: '欢迎来到小疯的简史生活',
        desc: '小疯的简史生活',
        imgUrl: 'https://img.jgrm.net/FtFFR92Qst6RpzsYGUsmknpZ-Z2g', // 交广领航普通LOGO
      },
    };
  },
  computed: {
    ...mapGetters([
      'currentSong',
      'currentIndex',
      'playList',
      'playing',
      'fullScreen',
      'radioInfo',
    ]),
    pageTitle() {
      return this.radioInfo.title
    },

  },
  created() {
    if (isInJglh) {
      // addWebViewListener(
      //   'back',
      //   () => {
      //     // 解决IOS中页面返回audio、video不能自动停止播放的问题，调用app提供的方法拦截返回(包括手势返回)事件
      //     let audios = document.getElementsByTagName('audio');
      //     let len = audios.length
      //     for (let i = 0; i < len; i++) {
      //       audios[i].pause();
      //     }
      //   },
      //   false
      // )
    }
  },
  destroyed() {
  },
  // deactivated() {
  //   back()
  // },
  methods: {
    ...mapActions(['select_play']),
    ...mapMutations([
      'SET_RADIO_INFO',
    ]),
    init() {
      getAudioProfile({ id: 1 })
        .then((profile) => {
          this.status = AppStatus.READY
          this.SET_RADIO_INFO(profile || {})
          this.share()
        })
        .catch((err) => {
          this.status = AppStatus.ERROR
          toast().tip(err)
          // return Promise.reject(err)
        });
    },
    onResume() {

    },
    initTheme() {
      // if (!storage.get(THEME_KEY)) {
      //   this.changeThems(storage.get(THEME_KEY, themes.white));
      // } else {
      //   this.changeThems(storage.get(THEME_KEY));
      // }
    },
    selectPlay() {
      // this.showPlayList = false
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = '/audio/share/album';
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh'
      });
      const desc = getHTMLTextContent(this.radioInfo.introduction, 100)
      const shareInfo = {
        link: link,
        title: `我正在收听《${this.radioInfo.title}》~`,
        desc: desc,
        imgUrl: getImageURL(this.radioInfo.image)
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    }
  }
};
</script>
<style lang="scss">
// 解决动态html样式无效，不能使用scoped
.radio-info {
  p {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #ffffff !important;
    line-height: 21px !important;
    div{
      display: none;
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #ffffff !important;
      line-height: 21px !important;
    }
  }
  &.explanded {
    p {
      div{
        display: block;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.profile-home{
  background: #7f5959;
}
.audio-wrap {
  width: 100%;
  height: 100%;
  margin-top: calc(45px + constant(safe-area-inset-top));
  margin-top: calc(45px + env(safe-area-inset-top));
  // height: calc(100vh - 45px);
  box-sizing: border-box;
  background: #7f5959;
  &.no-header {
    margin-top: constant(safe-area-inset-top);
    margin-top: env(safe-area-inset-top);
  }
}
.purple-header {
  background: #7f5959;
  color: #ffffff;
}
.radio-profile {
  padding: 15px;
  box-sizing: border-box;
  background: #7f5959;
  .card {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    align-items: center;
    color: #ffffff;
    margin-bottom: 12px;
    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 10px;
      margin-right: 16px;
    }
    .info {
      flex: 1;
      // line-height: 1;
      p {
        font-size: 18px;
        line-height: 22px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      span {
        display: block;
        font-size: 14px;
        line-height: 18px;
        opacity: 0.8;
      }
    }
    .icon-svg {
      font-size: 22px;
      transform: rotate(90deg);
    }
  }
  .radio-info {
    box-sizing: border-box;
    height: calc(100vh - 270px);
    // padding-bottom: 50px;
    overflow-y: scroll;
    p {
      font-size: 14px !important;
      font-weight: 500;
      color: #ffffff;
      line-height: 21px !important;
      text-align: justify;
      div{
        display: none;
        font-size: 14px !important;
        font-weight: 500;
        color: #ffffff;
        line-height: 21px !important;
      }
    }
    &>p{
      height: 63px !important;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
    }
    .expland-btn {
      float: right;
      margin-top: 12px;
      padding: 5px 10px;
      background: #8c6464;
      font-size: 10px;
      font-weight: 500;
      color: #fefefe;
      display: inline-flex;
      align-items: center;
      border-radius: 20px;
      line-height: normal;
      .icon-svg {
        margin-left: 5px;
        font-size: 16px;
        transform: rotate(180deg);
      }
      &:active {
        opacity: 0.8;
      }
    }
    &.explanded {
      // padding-bottom: 102px;
      &>p{
        height: auto !important;
      }
      p {
        -webkit-line-clamp: 100;
        div{
          display: block;
        }
      }
      .expland-btn {
        position: absolute;
        left: 50%;
        bottom: 66px;
        transform: translateX(-50%);
        .icon-svg{
          transform: rotate(0deg);
        }
      }
    }
  }
}
.playlist-pop {
  height: calc(100vh - 300px - constant(safe-area-inset-top));
  height: calc(100vh - 300px - env(safe-area-inset-top));
  overflow-y: hidden;
  box-sizing: border-box;
  &.show-mini-player{
    padding-bottom: 61px;
  }
}
.no-header .playlist-pop {
  height: calc(100vh - 255px - constant(safe-area-inset-top));
  height: calc(100vh - 255px - env(safe-area-inset-top));
}
.no-header .audio-wrap{
  margin-top: 0;
  padding-top: 0;
}
</style>
