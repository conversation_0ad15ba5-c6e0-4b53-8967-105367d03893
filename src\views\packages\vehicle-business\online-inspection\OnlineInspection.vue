<template>
  <container class="vehicle-inspection" @ready="init" @leave="onLeave" @resume="onResume" :keep-alive="keepAlive">
    <x-header title="上线审车预约">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view ref="content" :status="status" @reload="reload">
      <template v-if="status == AppStatus.READY">
        <c-picture class="banner" src="./images/banner.jpg">
        </c-picture>
        <!-- <div class="order-entrance android-inputing-hidden"  @click="$_router_pageTo(...pages.inspectionOrderList)">
          <span>审车</span>
          <span>订单</span>
        </div> -->

        <panel class="panel-rules">
          <div class="rules-head">
            <span class="rules-head__title">车辆要求：</span>
            <span class="rules-head__extra" @click="$_router_push({ name: 'VehicleBusinessQA' })">常见问题</span>
          </div>
          <div class="rules-body">
            <dl class="rules-list">
              <dd>
                <div class="warn">请认真检查以下车辆要求，若不符合以下要求则可能办理不成功且无法退款</div>
              </dd>
              <dd>1. 外观与行驶证照片中车辆保持一致，保持车身整洁，漆面完整；
              </dd>
              <!-- <dd>2. 不可以私自改装灯光系统，保证各灯光正常工作；
              </dd>
              <dd>3. 前排侧窗后视镜位置不能贴膜；
              </dd>
              <dd>4. 灭火器和三角警告牌是必备物品；
              </dd> -->
              <dd>2. 本服务受理范围为7座（含）以下私家车、网约车
              </dd>
            </dl>
          </div>
        </panel>

        <panel class="panel-steps">
          <h4 class="steps-head">办理流程：</h4>
          <ul class="steps">
            <li>
              <c-picture class="step-icon" src="./images/step-1.png"></c-picture>
              <h4 class="step-title">准备材料</h4>
              <div class="step-content">
                <p>请将以下所需材料备齐</p>
                <p class="content-title">个人车辆：</p>
                <p>1. 车主身份证；</p>
                <!-- <p>2. 有效期内交强险副本原件；</p> -->
                <p>2. 车辆行驶证原件正副本；</p>
                <p>3. 三角警告牌；</p>
                <p class="content-title">单位车辆：</p>
                <p>1. 单位公章；</p>
                <p>2. 经办人身份证原件及复印件一张；</p>
                <!-- <p>3. 有效期内交强险副本原件；</p> -->
                <p>3. 车辆行驶证原件正副本；</p>
              </div>
            </li>
            <li>
              <c-picture class="step-icon" src="./images/step-4-a.png"></c-picture>
              <h4 class="step-title">线上下单，检车点审车</h4>
              <div class="step-content">
                <!-- <p>1. 填写车主基本信息，下单支付审车费用；</p>
                <p>2. 到达检测站后直接联系检测站小秘书核销并服务；</p>
                <p>3. 审车完成；</p> -->
                <p>1. 交广领航0元下单预约；</p>
                <p>2. 小秘书联系确认时间及携带材料；</p>
                <p>3. 到站审车完成后找到预约订单并点击支付费用；</p>
                <p>4. 小秘书扫码核销。</p>
              </div>
            </li>
          </ul>
        </panel>

        <div class="button-sp-area ios-overlaying-hidden android-inputing-hidden" slot="foot">
          <a href="javascript:;" class="weui-btn weui-btn_primary" @click="doSubmit">立即办理</a>
        </div>
      </template>
    </content-view>
  </container>
</template>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.banner {
  width: 100%;
  height: 120px;
  position: relative;
}

.warn {
  color: rgb(20, 143, 241);
}

.order-btn {
  position: absolute;
  right: 0;
  z-index: 1;
  top: 10px;
  padding: 2px 8px;
  background: white;
  font-weight: 700;
  border-radius: 3px 0 0 3px;

  &::before {
    content: '\E622';
    font-family: iconfont;
    margin-right: 2px;
    color: #2196F3;
    font-size: 18px;
    font-weight: 400;
  }
}

.panel-rules {
  margin-top: 0;

  .rules-head {
    display: flex;
    margin: 3px 0;
    align-items: flex-end;

    .rules-head__title {
      flex: 1;
      font-weight: 700;
      font-size: 16px;
    }

    .rules-head__extra {
      color: #5a5a5a;
      font-size: 14px;

      &::after {
        color: #b1b1b1;
        font-family: iconfont;
        content: '\E605';
      }
    }
  }

  .rules-body {
    border: 1px solid #f5f3f3;
    padding: 0 5px;
    border-radius: 2px;
    margin: 8px auto;
  }
}

.panel-steps {
  .steps-head {
    font-size: 16px;
    margin: 10px 0;
  }
}

$item-border-color: #dadada;

.tip2 {
  font-size: 13px;
  text-align: center;
  color: rgb(33, 150, 243);
  margin-top: 5px;
}

.reserve-tip {
  font-size: 14px;
  color: #f11313;
  text-align: left;
  padding: 5px;
}

.block {
  background: white;
  padding: 10px;
}

.button-sp-area {
  // border-top: 1px solid rgba(228, 228, 228, 0.1);
  box-shadow: 0 1px 10px 1px rgba(35, 35, 35, 0.1);
  /* px */
  z-index: 0;
  padding: 10px;
  background: rgb(255, 255, 255);

  .weui-btn_disabled.weui-btn_primary {
    background-color: rgb(232, 232, 232);
    color: rgb(255, 255, 255);
  }
}

.title-line {
  color: black;

  >span {
    background: white;
  }
}

.rules-list {
  color: gray;
  margin: 10px 0;
  font-size: 0.9em;

  >dt {
    text-align: center;
    color: black;
    margin: 20px 5px 5px;
  }
}

.steps {
  .content-title {
    font-weight: 700;
    color: red;
  }

  .step-content {
    color: gray;
    font-size: 0.9em;
  }

  >li {
    margin-bottom: 10px;
    padding-left: 65px;
    list-style: none;
    position: relative;
  }

  .step-icon {
    width: 50px;
    height: 50px;
    background-color: rgb(226, 226, 226);
    border-radius: 50%;
    position: absolute;
    left: 5px;
    top: 0;
  }

  .step-title {
    margin-bottom: 5px;
  }
}

.order-entrance {
  position: fixed;
  z-index: 10;
  bottom: 80px;
  right: 3vw;
  background: #F9A001;
  background: linear-gradient(to bottom, rgb(255, 198, 3), rgb(244, 134, 0));
  background: url(../assets/images/order.png) center center no-repeat;
  background-size: cover;
  color: white;
  text-align: center;
  font-size: 14px;
  border-radius: 50%;
  border-radius: 50%;
  line-height: 1;
  display: flex;
  width: 50px;
  height: 50px;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 0 20px 1px rgba(0, 0, 31, 0.18);

  // transform: scale(0.9);
  &:active {
    opacity: 0.8;
  }

  >span {
    margin: 1px auto;
    display: none;
    // transform: scale(0.9);
  }

  /* &::before {
      font-family: iconfont;
      content: '\E622';
      display: block;
      font-size: 20px;
      margin-bottom: 1px;
    } */
}

.order-entrance2 {
  position: fixed;
  z-index: 10;
  bottom: 15vh;
  right: -10px;
  background: #388EFF;
  color: white;
  text-align: center;
  font-size: 16px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 1px 5px;
  box-shadow: 0 0 20px 1px rgba(0, 0, 31, 0.18);
  transition: 250ms all;

  >span {
    position: relative;
    left: -10px;
  }

  &:active {
    opacity: 0.8;
  }

  &::before {
    font-family: iconfont;
    content: '\E622';
    display: block;
    font-size: 17px;
    margin-right: 2px;
    position: relative;
    left: -10px;
  }

  &::after {
    content: "";
    border-radius: 50%;
    height: 100%;
    width: 25px;
    position: absolute;
    left: 0;
    background: rgb(56, 142, 255);
    transform: translateX(-50%);
    z-index: -1;
  }
}
</style>
<script>
import { formatDate, getFullURL, getAbsolutePath } from '@/utils';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { AppStatus, OrderType } from '@/enums';
import ENV from '@/common/env';
// import { jglh } from '@/common/env';
import { loading, dialog, toast, back } from '@/bus';
import { login, setTitle, playPhotos } from '@/bridge';

const PHONE = '************';
const isAndroid = ENV.android;
const isInWeixin = ENV.weixin;
const isInApp = ENV.jglh;

const ContentType = {
  A: 1,
  B: 2,
};

const VehicleLicense = {
  REG_TIME: 1,
  VALID_TIME: 2,
};

export default {
  name: 'OnlineInspection',
  components: {},
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    const now = new Date();
    // console.log(ENV);
    return {
      AppStatus,
      ContentType,
      VehicleLicense,
      PHONE,
      status: AppStatus.LOADING,
      keepAlive: true,
      page: {},
    }
  },
  computed: {
    couldSubmit() {
      return true;
    },
  },
  mounted() { },
  methods: {
    initPageData() {
      this.status = AppStatus.READY;
      this.$_auth_checkSession();
    },
    init() {
      console.log('init...');
      if (isInWeixin) {
        setTitle('交广审车');
      }
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    callPhone() {
      window.open(`tel:${this.PHONE}`);
    },
    doSubmit() {
      this.$_auth_checkSession().then(res => {
        res.ok ? this.submit() : this.$_auth_login();
      }).catch(e => {
        e && dialog().alert(e);
      });
    },
    async submit() {
      try {
        // if (this) {
        //   dialog().alert(`根据河南省人民政府“关于启动重大突发公共卫生事件一级响应的决定”，为切实做好疫情防控工作，车务相关业务暂停接单，恢复时间另行通知`, {
        //     title: '提示'
        //   });
        //   return;
        // }
        // if (!this.form.read) {
        //   dialog('提醒').alert('请阅读并勾选审车须知');
        //   return;
        // }
        // const form = this.form2;
        this.$_auth_push({
          name: 'OnlineInspectionShopList',
          query: this.$route.query,
          // path: '/vehicle-business/online-inspection/shops',
        });
      } catch (e) {
        e && dialog().alert(e, {
          title: '提示',
        });
      } finally {
        loading(false);
      }
    },
  }
};
</script>
