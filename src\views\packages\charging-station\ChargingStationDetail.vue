<template>
  <container class="charging-station-detail">
    <x-header :title="'充电站详情'">
      <x-button slot="left" type="back"></x-button>
      <!-- {{ AURA-X: Modify - 参考OnlineInspectionShop的分享按钮实现. Confirmed via 寸止 }} -->
      <div v-if="status == AppStatus.READY" slot="right">
        <div slot="right" class="share">
          <i class="icon_jglh icon-fenxiang1" @click="share"></i>
        </div>
      </div>
    </x-header>

    <content-view
      :status="status"
      :refresh-action="refreshAction"
      @refresh="refresh"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <div class="station-page">
          <!-- 顶部图片 -->
          <div v-if="stationImages.length" class="station-photos">
            <biz-image
              v-for="(image, index) in stationImages"
              :key="index"
              class="station-photo"
              :src="image"
              @click="playStationPhotos(index)"
            >
            </biz-image>
          </div>

          <!-- 站点信息卡片 -->
          <div class="station-info">
            <h2 class="title">{{ station.name || '充电站' }}</h2>
            <div class="sub-info">
              <span v-if="station.rating" class="score">{{ station.rating }}分</span>
              <span class="success">{{ getStationStatusText() }}</span>
            </div>
            <div v-if="stationTags.length" class="tags">
              <span
                v-for="tag in stationTags"
                :key="tag.text"
                class="tag"
                :class="tag.type"
              >
                {{ tag.text }}
              </span>
            </div>
            <div class="station-address">
              <div class="address-content">
                <div class="address-text">
                  <i
                    class="icon_jglh icon-a-sc-weizhidizhidingwei address-icon"
                  ></i>
                  <span class="address-label">{{ station.address || '地址信息暂无' }}</span>
                </div>
                <div class="action-buttons">
                  <div
                    class="action-btn navigation-btn"
                    @click="navigateToStation"
                  >
                    <i class="icon_jglh icon-sc-daohang btn-icon"></i>
                    <span class="btn-text">导航</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务设施 -->
          <!-- <div class="services">
            <div class="service-item"><i class="icon">🅿️</i> 专用通道</div>
            <div class="service-item"><i class="icon">☕</i> 休息室</div>
            <div class="service-item"><i class="icon">💡</i> 场站照明</div>
          </div> -->

          <!-- 充电桩信息 -->
          <div class="charge-info" v-if="stationChargeTypes.length">
            <div
              class="charge-box"
              :class="{ 'single-type': stationChargeTypes.length === 1 }"
            >
              <div
                v-for="chargeType in stationChargeTypes"
                :key="chargeType.type"
                class="charge-item"
                :class="chargeType.type"
              >
                <div class="type-tag">{{ chargeType.name }}</div>
                <div class="status">
                  共 <span class="total-count">{{ chargeType.total }}</span> 个
                </div>
                <div class="power">最大功率 {{ chargeType.maxPower }}kW</div>
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div v-if="station.pricePerKwh || parkingFeeDisplay" class="price-info">
            <h3>价格信息</h3>
            <div v-if="station.pricePerKwh" class="current-price">
              <span class="value">{{ station.pricePerKwh.toFixed(4) }}</span>
              <span class="unit">元/度</span>
            </div>
            <div v-if="businessHoursDisplay" class="time-price">
              营业时间：{{ businessHoursDisplay }}
            </div>
            <div v-if="parkingFeeDisplay" class="parking">
              🅿️ 停车费用：<span :class="getParkingFeeClass()">{{ parkingFeeDisplay }}</span>
            </div>
          </div>

          <!-- 服务设施 -->
          <div v-if="servicesList.length" class="services-info">
            <h3>服务设施</h3>
            <div v-if="servicesList.length" class="service-tags">
              <span class="service-label">服务：</span>
              <span
                v-for="service in servicesList"
                :key="service"
                class="service-tag"
              >
                {{ service }}
              </span>
            </div>
            <!-- <div v-if="benefitsList.length" class="benefit-tags">
              <span class="benefit-label">权益：</span>
              <span
                v-for="benefit in benefitsList"
                :key="benefit"
                class="benefit-tag"
              >
                {{ benefit }}
              </span>
            </div> -->
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { AppStatus, ImageType } from '@/enums';
import { toast } from '@/bus';
import { navigate, playPhotos } from '@/bridge';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { getChargingStationDetail } from '@/api/modules/charging-station';
import { Icon, Tag, Rate, Button } from 'vant';
import {
  getStatusText,
  getTagType,
  getServiceIcon,
  getChargerTypes,
  getStationTags,
  getBusinessHoursTypeText,
  getServicesText,
  getBenefitsText,
  formatVoltageRange,
  getParkingFeeTypeText,
  getOperationTypeText,
} from './utils';
export default {
  name: 'StationPage',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Rate.name]: Rate,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 0,
      station: {},

    };
  },
  computed: {
    // {{ AURA-X: Add - 分享信息计算属性，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
    shareInfo() {
      const title = '我发现了一个不错的充电站，充电快服务好，推荐！';
      const desc = this.station.name || '充电站详情';
      const logo =
        this.stationImages.length > 0
          ? getImageURL(this.stationImages[0], '_xs')
          : getImageURL('Fi6QQSe98E9zHC9EKCKBiTFiRNFo_xlogo', '_xs'); // 默认logo
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      return shareInfo;
    },

    // 充电站图片列表 - 从API数据解析
    stationImages() {
      if (!this.station.images) {
        return [];
      }
      return this.station.images.split(',').filter(img => img.trim());
    },

    // 充电桩类型信息 - 使用API数据
    stationChargeTypes() {
      return getChargerTypes(this.station);
    },

    // 充电站标签
    stationTags() {
      return getStationTags(this.station);
    },

    // 营业时间显示
    businessHoursDisplay() {
      const typeText = getBusinessHoursTypeText(this.station.businessHoursType);
      if (this.station.businessHoursDesc) {
        return `${this.station.businessHoursDesc}`;
      }
      return typeText;
    },

    // 服务设施列表
    servicesList() {
      return getServicesText(this.station.services || []);
    },

    // 权益列表
    // benefitsList() {
    //   return getBenefitsText(this.station.benefits || []);
    // },

    // 电压范围显示
    voltageRangeDisplay() {
      return formatVoltageRange(
        this.station.minVoltage,
        this.station.maxVoltage
      );
    },

    // 停车费信息
    parkingFeeDisplay() {
      const feeText = getParkingFeeTypeText(this.station.parkingFeeType);
      if (this.station.parkingFeeTypeDesc) {
        return `${this.station.parkingFeeTypeDesc}`;
      }
      return feeText;
    },

    // 运营类型显示
    operationTypeDisplay() {
      return getOperationTypeText(this.station.operationType);
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { id } = this.$route.params;
      if (!id) {
        toast().tip('充电站ID不能为空');
        this.$_router_back();
        return;
      }
      this.getStationDetail(id);
    },

    // 获取充电站详情
    getStationDetail(stationId) {
      this.status = AppStatus.LOADING;
      getChargingStationDetail(stationId)
        .then(res => {
          // {{ AURA-X: Modify - 处理API返回的数据结构. Confirmed via 寸止 }}
          // 根据接口文档，返回的是 Result«StationVO» 结构
          if (res && res.success && res.data) {
            this.station = res.data;
          } else if (res && !res.data) {
            // 兼容直接返回station对象的情况（mock数据）
            this.station = res;
          } else {
            throw new Error(res.msg || '获取充电站详情失败');
          }

          // {{ AURA-X: Add - 数据加载完成后更新分享信息. Confirmed via 寸止 }}
          this.updateShare();
          this.status = AppStatus.READY;
        })
        .catch(e => {
          console.error('获取充电站详情失败:', e);
          this.status = AppStatus.ERROR;
          const errorMsg = typeof e === 'string' ? e : (e.message || '获取充电站详情失败');
          toast().tip(errorMsg);
        });
    },

    // 刷新
    refresh() {
      const { id } = this.$route.params;
      this.getStationDetail(id);
      this.refreshAction = Date.now();
    },

    // 重新加载
    reload() {
      this.init();
    },

    // 使用工具函数
    getStatusText,
    getTagType,
    getServiceIcon,

    // 拨打电话
    callPhone() {
      if (this.station.phone) {
        window.location.href = `tel:${this.station.phone}`;
      }
    },

    // 导航到充电站
    navigateToStation() {
      const { address, latitude, longitude, name } = this.station;
      navigate({
        address: address || '地址信息暂无',
        name: name || '充电站',
        longitude: longitude,
        latitude: latitude,
        callback() {},
      });
    },

    // 获取充电站状态文本
    getStationStatusText() {
      if (this.station.status === 'OPEN') {
        return '营业中';
      } else if (this.station.status === 'CLOSED') {
        return '暂停营业';
      }
      return '状态未知';
    },

    // 获取停车费样式类
    getParkingFeeClass() {
      if (this.station.parkingFeeType === 'FREE' ||
          (this.station.parkingFeeTypeDesc && this.station.parkingFeeTypeDesc.includes('免费'))) {
        return 'free';
      }
      return 'paid';
    },

    // {{ AURA-X: Add - 图片预览方法，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
    // 预览图片通用方法
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i + 1}`,
          url: getImageURL ? getImageURL(item, ImageType.MEDIUM) : item,
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },

    // 预览充电站图片
    playStationPhotos(index) {
      this.playPhotos(this.stationImages, index);
    },

    // {{ AURA-X: Modify - 使用mixinShare的分享方法，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
    updateShare() {
      this.$_share_update(this.shareInfo);
    },
    share() {
      this.$_share(this.shareInfo);
    },
  },
};
</script>

<style lang="scss" scoped>
// {{ AURA-X: Add - 分享按钮样式，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}

.station-page {
  color: #333;

  .station-photos {
    // {{ AURA-X: Modify - 固定尺寸横向滚动布局. Confirmed via 寸止 }}
    display: flex;
    position: relative;
    background: #eef2f7;
    padding: 8px;
    gap: 8px;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    .station-photo {
      flex-shrink: 0; /* 防止图片被压缩 */
      width: 160px; /* 固定宽度 */
      height: 120px; /* 固定高度 */
      border-radius: 8px;
      position: relative;
      overflow: hidden;
      cursor: pointer;

      .img-count {
        position: absolute;
        right: 8px;
        bottom: 8px;
        display: inline-block;
        min-width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.6);
        text-align: center;
        border-radius: 10px;
        line-height: 20px;
        font-size: 12px;
        color: #fff;
        padding: 0 6px;
        font-weight: 500;
      }

      // 图片加载失败时的占位样式
      &::before {
        content: '充电站图片';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 13px;
        color: #666;
        z-index: 1;
      }
    }
  }

  .station-info {
    background: #fff;
    margin: 10px;
    padding: 12px;
    border-radius: 8px;

    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }

    .sub-info {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;

      .score {
        margin-right: 10px;
      }
      .success {
        color: #4caf50;
      }
    }

    .tags {
      margin-bottom: 10px;
      .tag {
        display: inline-block;
        background: #f2f2f2;
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
        margin: 2px 4px 2px 0;
      }
    }

    .station-address {

      .address-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .address-text {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        line-height: 1.4;

        .address-icon {
          font-size: 16px;
          color: #fd4925;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .address-label {
          flex: 1;
          word-break: break-all;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        margin-left: 16px;
        flex-shrink: 0;
      }

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-width: 48px;
        min-height: 48px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: #e9ecef;
        }

        .btn-icon {
          width: 24px;
          height: 24px;
          font-size: 16px;
          color: #fd4925;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .btn-text {
          font-size: 11px;
          color: #666;
          font-weight: 500;
          line-height: 1;
        }

        &.navigation-btn:hover {
          background: #fff5f4;

          .btn-icon {
            color: #e63946;
          }
        }

        &.phone-btn:hover {
          background: #f0f9ff;

          .btn-icon {
            color: #0284c7;
          }

          .btn-text {
            color: #0284c7;
          }
        }
      }
    }
  }

  .services {
    background: #fff;
    margin: 0 10px 10px;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    font-size: 13px;

    .service-item {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 4px;
      }
    }
  }

  .charge-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    .charge-box {
      display: flex;
      gap: 8px;

      // {{ AURA-X: Add - 单类型布局优化. Confirmed via 寸止 }}
      &.single-type {
        justify-content: center;

        .charge-item {
          max-width: 300px;
        }
      }

      .charge-item {
        flex: 1;
        padding: 16px 12px;
        border-radius: 12px;
        font-size: 13px;
        position: relative;
        border: 1px solid transparent;

        .type-tag {
          // {{ AURA-X: Add - 类型标签样式，类似tag+斜体. Confirmed via 寸止 }}
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: bold;
          font-style: italic;
          margin-bottom: 8px;
          color: #fff;
        }

        .status {
          margin-bottom: 6px;
          font-size: 14px;
          color: #666;

          // {{ AURA-X: Modify - 总数量显示样式. Confirmed via 寸止 }}
          .total-count {
            font-size: 18px;
            font-weight: bold;
            margin: 0 2px;
            color: #333;
          }
        }

        .power {
          font-size: 12px;
          color: #999;
        }
      }

      // {{ AURA-X: Add - 各类型主题色. Confirmed via 寸止 }}
      .super {
        background: linear-gradient(135deg, #fff5f6 0%, #ffe8ea 100%);
        border-color: #f93b51;

        .type-tag {
          background: #f93b51;
        }

        .status .total-count {
          color: #f93b51;
        }
      }

      .fast {
        background: linear-gradient(135deg, #fffaf5 0%, #ffedd5 100%);
        border-color: #ff9330;

        .type-tag {
          background: #ff9330;
        }

        .status .total-count {
          color: #ff9330;
        }
      }

      .slow {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #30c856;

        .type-tag {
          background: #30c856;
        }

        .status .total-count {
          color: #30c856;
        }
      }

      .dc {
        background: linear-gradient(135deg, #fffaf5 0%, #ffedd5 100%);
        border-color: #ff9330;

        .type-tag {
          background: #ff9330;
        }

        .status .total-count {
          color: #ff9330;
        }
      }

      .ac {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #30c856;

        .type-tag {
          background: #30c856;
        }

        .status .total-count {
          color: #30c856;
        }
      }
    }
  }

  .price-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    h3 {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .current-price {
      font-size: 18px;
      color: #ff3300;
      font-weight: bold;

      .value {
        font-size: 20px;
      }
      .unit {
        font-size: 13px;
        margin-left: 4px;
      }
    }

    .time-price {
      margin-top: 6px;
      font-size: 13px;
      color: #666;

      .more {
        float: right;
        color: #1989fa;
      }
    }

    .parking {
      margin-top: 10px;
      font-size: 13px;
      .free {
        color: #4caf50;
        margin-left: 4px;
      }
      .paid {
        color: #ff9330;
        margin-left: 4px;
      }
    }
  }

  // {{ AURA-X: Add - 服务设施信息样式. Confirmed via 寸止 }}
  .services-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    h3 {
      font-size: 14px;
      margin-bottom: 12px;
      color: #333;
    }

    .service-tags,
    .benefit-tags {
      margin-bottom: 8px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .service-label,
    .benefit-label {
      font-size: 13px;
      color: #666;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .service-tag,
    .benefit-tag {
      display: inline-block;
      background: #f2f2f2;
      border-radius: 12px;
      padding: 4px 8px;
      font-size: 12px;
      margin: 2px 4px 2px 0;
      color: #666;
    }

    .benefit-tag {
      background: #fff5f6;
      color: #fd4925;
      border: 1px solid #ffe8ea;
    }
  }
}
</style>
