<template>
  <container class="vehicle-index" @ready="init" mode="fullscreen">
    <x-header :title="pageTitle" :fullscreen-config="headerFullscreenConfig">
      <x-button slot="left" type="back"></x-button>
      <!-- <x-button
        slot="right"
        type="text"
        @click="$_auth_push({ name: 'VehicleBusinessOrders'})"
      >车务订单</x-button> -->
    </x-header>
    <content-view :status="status" @reload="reload" @scroll="onPageScroll">
      <div class="banner">
        <h2>交管车务<span> 服务预约</span></h2>
        <div class="fix_icon"></div>
        <!-- 右侧按钮 -->
        <div class="float-btn" @click="$_auth_push({ name: 'VehicleBusinessOrders' })">
          车务订单
        </div>
      </div>
      <div class="main-content">
        <swiper v-if="banners.length" class="banner-container" ref="slider" :options="{ autoplay: true }"
          :pagination="banners.length > 1" :allow-empty="true" v-model="currentSwiperIndex">
          <swiper-item v-for="(item, index) in banners" :key="index">
            <biz-image class="banner-image" :src="item.image" @click="onBannerClick(item)"
              type="?imageView2/1/format/jpg/q/90" fill="fill">
            </biz-image>
          </swiper-item>
          <div slot="extra" class="swiper-counts">
            {{ currentSwiperIndex + 1 }}/{{ banners.length }}
          </div>
        </swiper>
        <!-- 领取工会优惠券 -->
        <div class="box-c" v-if="isHnzghApp" @click="handleGetCoupon">
          <div class="block-box">
            <div class="coupon-wrap">
              <c-picture class="vip-banner" fill="contain" autoFit="height" :src="couponBanner">
              </c-picture>
              <!-- <van-button
                class="renew"
                color="#FD4925"
                round
                type="primary"
                size="small"
                :loading="btnLoading"
                :disabled="disabledCoupon"
                loading-text="领取中..."
                @click="handleGetCoupon"
                ></van-button
              > -->
            </div>
          </div>
        </div>

        <ul class="btn-list">
          <li class="btn-item" v-for="(item, index) in list" :key="index">
            <biz-image class="btn-item__icon" :src="item.icon"></biz-image>
            <div class="btn-item__name">{{ item.name }}</div>
            <van-button class="renew" color="#F3F3F3" round type="primary" size="small"
              @click="go(item)">去查看</van-button>
          </li>
        </ul>
        <!-- <div class="flex-wrap">
          <div class="flex-item">
            <span>查油价</span>
            <van-icon name="arrow" color="#333333" size="18" />
          </div>
          <div class="flex-item">
            <span>附近检测点</span>
            <van-icon name="arrow" color="#333333" size="18" />
          </div>
        </div> -->
      </div>
      <FloatSideButton v-if="showOrderBtn" :width="80" :height="80" :coefficientHeight="0.85" class="apply-f">
        <div class="f-btn-apply" @click="handleOrderBtnClick">
          <img src="./images/order_btn.png" alt="apply" />
        </div>
      </FloatSideButton>
    </content-view>
  </container>
</template>
<script>
import { AppStatus } from '@/enums';
import { isInJglh, isInWeixin, isInWeApp, isProduction } from '@/common/env';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { canIUseJglh } from '@/bridge';
import { getImageURL } from '@/common/image';
import { getAppURL } from '@/utils';
import { dialog, toast } from '@/bus';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import { Swiper, SwiperItem } from '@/components/Swiper';
import { getInspectionBanners } from '../api';
import { getCoupon } from '@pkg/ticket/api';
import { getSessionThirdAppParams } from '@/store/storage';

import { Icon, Button, Toast } from 'vant';
import FloatSideButton from '@/views/components/FloatSideButton.vue';

function couldUseIn(envList, currentEnv) {
  return envList.indexOf(currentEnv) != -1;
}

const Platform = {
  JGLH: 'jglh', // 交广领航
  WEIXIN_WEB: 'wechat-web', // 微信网页
  WEAPP: 'weapp', // 小程序
};
// 2020年9月23日 11:35:58，为规避苹果审核，根据部门领导指示，按钮名称后加预约字样
// 2021年8月25日 10:42:54，下线年检计算器、六年免检预约、证牌补换预约

export default {
  name: 'VehicleBusinessIndex',
  components: {
    Swiper,
    SwiperItem,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    FloatSideButton,
  },
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      category: this.$route.query.category,
      cooperationChannel: this.$route.query.cooperationChannel,
      currentSwiperIndex: 0,
      banners: [],
      $_share_info: {
        path: '/vehicle-business',
        title: '交管车务',
        desc: '车辆年检、证牌补换、新车上牌、二手车过户等等，使用交广领航，足不出户，统统搞定！',
        imgUrl: isInWeApp ? getImageURL('FpLDUAkecqT8wYKh2VjUTogTDUip') : '',
      },
      couponBanner: require('@pkg/vehicle-business/business-index/images/coupon.png'),
      loading: false,
      disabledCoupon: false,
      btnLoading: false,
      showOrderBtn: true,
      scrollTop: 0,
      jglhBtnList: [
        {
          name: '年检计算器',
          path: '/vehicle-business/calculator',
          icon: require('./images/vehicle-calculator.png'),
          options: {
            requireSignIn: false,
            sbarColor: 'white',
            // sbarBgColor: '#388efd',
            // titleBar: true,
          },
        },
        // {
        //   name: '六年免检预约',
        //   path: '/vehicle-business/no-exam-inspection',
        //   icon: require('./images/no-exam-inspection.png'),
        //   options: {
        //     titleBar: true,
        //     sbarBgColor: '#388efd',
        //     sbarColor: 'white',
        //     requireSignIn: false,
        //     // sbarColor: 'black',
        //     // sbarBgColor: '#f2f2f2',
        //   },
        // },
        {
          name: '上线审车预约',
          // path: '/vehicle-business/online-inspection',
          path: '/vehicle-business/online-inspection/shops',
          icon: require('./images/online-inspection.png'),
          options: {
            titleBar: false,
            requireSignIn: false,
            sbarColor: 'white',
            // sbarBgColor: '#388dfc',
          },
        },
        {
          name: '代驾审车',
          path: '/vehicle-business/agent-inspection',
          icon: require('./images/agent-inspection.png'),
          options: {
            requireSignIn: true,
            sbarColor: 'white',
            // sbarBgColor: '#388efd',
            titleBar: false,
          },
        },
        // {
        //   name: '证牌补换预约',
        //   path: '/vehicle-business/card-replacement',
        //   icon: require('./images/card-replacement.png'),
        //   options: {
        //     sbarBgColor: '#388efd',
        //     sbarColor: 'white',
        //     titleBar: false,
        //   },
        // },
        // {
        //   name: '违章查询',
        //   path: 'https://radio.jgrm.net/actions/app/peccancy/peccancy.html?type=1',
        //   icon: require('./images/violation-query.png'),
        //   env: ['jglh', 'wechat-web'],
        //   options: {
        //     title: '违章查询',
        //     titleBar: false,
        //     shareButton: false,
        //   },
        // },
        {
          name: '新车上牌预约',
          path: '/vehicle-business/car-register',
          icon: require('./images/new-car-register.png'),
          options: {
            requireSignIn: false,
            titleBar: false,
            // sbarBgColor: '#388efd',
            sbarColor: 'white',
            shareButton: false,
          },
        },
        {
          name: '二手车过户',
          path: '/vehicle-business/car-transfer',
          icon: require('./images/2hand-car-transfer.png'),
          options: {
            requireSignIn: false,
            titleBar: false,
            // sbarBgColor: '#388efd',
            sbarColor: 'white',
            shareButton: false,
          },
        },
        {
          name: '外地车转入郑州',
          path: '/vehicle-business/carTransferToZz',
          icon: require('./images/car-transfer-to-zz.png'),
          options: {
            requireSignIn: false,
            titleBar: false,
            // sbarBgColor: '#388efd',
            sbarColor: 'white',
            shareButton: false,
          },
        },
        // {
        //   name: '违法代缴',
        //   path: 'https://ur.alipay.com/4rwQ',
        //   icon: require('./images/fine-pay.png'),
        //   env: ['jglh'],
        //   options: {
        //     title: '违法代缴',
        //     titleBar: false,
        //     shareButton: false,
        //   },
        // },
      ],
      btnListForCCB: [
        {
          name: '120元审车',
          path: '/vehicle-business/online-inspection?category=ccb',
          icon: require('./images/ccb/icon-inspection.png'),
          options: {
            titleBar: false,
            requireSignIn: false,
            sbarColor: 'white',
            sbarBgColor: '#388dfc',
          },
        },
        {
          name: '1分钱洗车',
          path: '/shops/wash?category=33&mode=zone&title=%E5%BB%BA%E8%A1%8C%E4%B8%80%E5%88%86%E9%92%B1%E6%B4%97%E8%BD%A6',
          icon: require('./images/ccb/icon-carwash.png'),
          options: {
            titleBar: false,
            requireSignIn: false,
            sbarColor: 'white',
            sbarBgColor: '#388dfc',
          },
        },
        {
          name: '建行ETC',
          path: '/vehicle-business/ccb?type=etc',
          icon: require('./images/ccb/icon-etc.png'),
          options: {
            titleBar: true,
            requireSignIn: false,
          },
        },
        {
          name: '建行信用卡',
          path: '/vehicle-business/ccb?type=credit-card',
          icon: require('./images/ccb/icon-credit-card.png'),
          options: {
            titleBar: true,
            requireSignIn: false,
          },
        },
        {
          name: '汽车分期',
          path: '/vehicle-business/ccb?type=buycar',
          icon: require('./images/ccb/icon-buycar.png'),
          options: {
            titleBar: true,
            requireSignIn: false,
          },
        },
        {
          name: '小微企业贷',
          path: '/vehicle-business/ccb?type=loan',
          icon: require('./images/ccb/icon-loan.png'),
          options: {
            titleBar: true,
            requireSignIn: false,
          },
        },
        {
          name: '千升油卡',
          path: '/vehicle-business/ccb?type=oil',
          icon: require('./images/ccb/icon-oil.png'),
          options: {
            titleBar: true,
            requireSignIn: false,
          },
        },
      ],
    };
  },
  computed: {
    // 是否是河南总工会app
    isHnzghApp() {
      const sessionParams = getSessionThirdAppParams();
      if (sessionParams.platform == 'hnzgh') return true;
      return false;
    },
    isCCB() {
      return this.category === 'ccb';
    },
    pageTitle() {
      if (this.isCCB) return '建行专属';
      return '交管车务服务预约';
    },
    // 全屏模式下 header配置
    headerFullscreenConfig() {
      return {
        // backgroundColor: 'rgba(56, 142, 253, 0)',
        backgroundColor: 'rgba(255, 255, 255, 0)',
        fullscreenHeight: window.innerWidth,
        scrolled: this.scrollTop,
      };
    },
    banner() {
      if (this.isCCB) return require('./images/banner-for-ccb.png');
      // return require('./images/banner-20200415.jpg');
      // return require('./images/banner-20200529.jpg');
      // return require('./images/banner-20200715.png');
      // return require('./images/banner-20200804.png');
      return require('./images/banner-20200914.png');
      // return require('./images/banner.jpg');
    },
    list() {
      if (this.category === 'ccb') return this.btnListForCCB;
      return this.jglhBtnList;
      // // 2023-12-07 11:35:58 交广领航提审需求：隐藏上门代审功能
      // return this.jglhBtnList.filter((item) => {
      //   return !(isInJglh && item.path == '/vehicle-business/agent-inspection')
      // });
    },
    shouldHideHeader() {
      return isInJglh;
    },
  },
  methods: {
    handleGetCoupon(data) {
      let errArr = {
        1: '此优惠券只允许VIP会员领取 您还不是VIP会员哦！',
        2: '此优惠券被领完了哦 去看看其它优惠券吧！',
        3: '此优惠券只允许领取一次，您已经领取过了哦！',
        4: '您已经领过此优惠券了，使用后再来领取吧！',
        5: '您领取此优惠券次数太多了，去看看其它优惠券吧！',
      };
      if (this.loading) {
        return;
      }
      this.loading = true;
      // 针对豫工会app 发放的20元优惠券，优惠券id固定
      // IDV216764480599661931 测试环境
      // IDV216764478951341723 正式环境
      getCoupon({
        id: isProduction ? 'IDV216764478951341723' : 'IDV216764480599661931',
      })
        .then(res => {
          this.loading = false;
          /**
           * 1、如果是只允许领取一次
           */
          if (res.status == 0) {
            // 领取成功
            dialog('领取成功').alert('在我的-优惠券中查看', {
              then: () => {
                // this.refreshQuietly();
                this.disabledCoupon = true;
              },
            });
          } else {
            dialog('提示').alert(errArr[res.status], {
              then: () => {
                // this.refreshQuietly();
                this.disabledCoupon = true;
              },
            });
          }
        })
        .catch(err => {
          this.loading = false;
          dialog('提示').alert(err.toString());
        });
    },
    onBannerClick(item) {
      const BannerType = {
        Link: 1,
        Content: 0,
      };
      if (item.type === BannerType.Content) {
        dialog().alert(item.content, {
          title: '提示',
        });
      } else {
        this.$_router_pageTo(item.url, { titleBar: true });
      }
    },
    go(item) {
      if (item.env) {
        if (isInWeApp && !couldUseIn(item.env, 'weapp')) {
          dialog().confirm(
            `${item.name}在小程序中无法使用，如需使用请下载交广领航App`,
            {
              title: '提示',
              // okText: '去下载',
              // ok() {
              //   location.href = 'http://www.jgrm.net'
              // }
            }
          );
          return;
        }
        if (isInWeixin && !couldUseIn(item.env, 'wechat-web')) {
          dialog().confirm(
            `${item.name}在微信中无法使用，如需使用请下载交广领航App`,
            {
              title: '提示',
              // okText: '去下载',
              // ok() {
              //   location.href = 'http://www.jgrm.net'
              // }
            }
          );
          return;
        }
      }
      if (item.options.requireSignIn && !this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      let blackList = ['上线审车预约', '代驾审车']; // 新车上牌预约 二手车过户 外地车转入郑州
      if (
        item.name != '年检计算器' &&
        blackList.includes(item.name) &&
        this.checkTime()
      ) {
        return;
      }
      // if (item.name == '上线审车预约') {
      //   this.$_router_push({
      //     name: 'OnlineInspectionShopList',
      //     query: this.$route.query,
      //     // path: '/vehicle-business/online-inspection/shops',
      //   });
      // } else {
      //   let params = (/\?/.test(item.path) ? '&' : '?') + ['cooperationChannel', this.$route.query.cooperationChannel].join('=');
      //   this.$_router_pageTo(item.path + params, item.options);
      // }
      let params =
        (/\?/.test(item.path) ? '&' : '?') +
        new URLSearchParams(this.$route.query).toString();
      this.$_router_pageTo(item.path + params, item.options);
    },
    handleOrderBtnClick() {
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      let query = '';
      if (this.$route.query.cooperationChannel) {
        query = '?' + ['cooperationChannel', this.$route.query.cooperationChannel].join('=');
      }
      this.$_router_pageTo(`/vehicle-business/reserve/order${query}`);
    },
    init() {
      this.checkTime('init');
      getInspectionBanners()
        .then(res => {
          this.banners = res;
          this.status = AppStatus.READY;
          // dialog().alert(`
          //   温馨提示:春节放假期间,检测站暂停审车服务，2月7日以后检测站陆续开始服务，下单完成后可按照提示和小秘书进行预约，感谢您的支持,祝您新春愉快、虎年大吉！
          //   `, {
          //   title: '通知'
          // });
        })
        .catch(err => {
          toast().tip(err);
          this.status = AppStatus.ERROR;
        });
    },
    share(action = 'config') {
      // 设置邀请链接分享信息
      let path = this.$route.fullPath;
      const inviteURL = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      });
      const shareInfo = {
        link: inviteURL,
        title: '交管车务',
        desc: '交管车务',
        imgUrl: isInWeApp ? getImageURL('FpLDUAkecqT8wYKh2VjUTogTDUip') : '',
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    // 返回true-不可用，false-可用
    // type参数：check-检查是否可用，init-初始化，初始化不弹窗
    checkTime(type = 'check') {
      const { available, message } = checkServiceStatus(
        ServiceEnum.CAR_INSPECTION
      );
      if (!available && type === 'check') {
        dialog().alert(message, {
          title: '通知',
        });
      }
      return !available;
    },
    reload() { },
    onPageScroll(top) {
      this.scrollTop = top;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.vehicle-index {
  background: #f3f3f3 url(./images/banner_2023.png) no-repeat left top;
  background-size: 100% auto;
}

.banner {
  padding-top: calc(80px + constant(safe-area-inset-top));
  padding-top: calc(80px + env(safe-area-inset-top));
  height: calc(140px + constant(safe-area-inset-top));
  height: calc(140px + env(safe-area-inset-top));
  box-sizing: border-box;
  // background: $lh-2022-primary-color url(../../assets/banner_icon.png) no-repeat 206px bottom;
  // background-size: 154px 106px;
  // background: linear-gradient(-90deg, #fd6937, #fa4135);
  // border-radius: 0px 0px 20px 20px;
  position: relative;

  h2 {
    padding-left: 15px;
    font-size: 30px;
    font-weight: 800;
    color: #333333;
    display: flex;
    align-items: flex-end;
    line-height: 1;

    span {
      font-size: 18px;
    }
  }

  .fix_icon {
    position: absolute;
    right: 26px;
    bottom: -7px;
    z-index: 2;
    width: 124px;
    height: 84px;
    background: url(./images/banner_fixed_icon.png) no-repeat center;
    background-size: 100% 100%;
  }
}

.float-btn {
  background: #ffffff;
  opacity: 0.6;
  line-height: 1;
  padding: 6px 7px 6px 12px;
  line-height: 1;
  text-align: center;
  border-radius: 12px 0px 0px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #333333;
  position: absolute;
  right: 0;
  top: calc(48px + constant(safe-area-inset-top));
  top: calc(48px + env(safe-area-inset-top));
  z-index: 2;
}

.main-content {
  padding: 15px;
  background: #f3f3f3;
  border-radius: 0px 30px 0px 0px;
}

.banner-container {
  width: 100%;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;

  .swiper-slide {
    border-radius: 10px;
    overflow: hidden;
  }

  .banner-image {
    width: 100%;
    height: 80px;
    overflow: hidden;
  }
}

.btn-list {
  list-style: none;
  // display: flex;
  // flex-wrap: wrap;
  margin-top: 15px;

  .btn-item ::v-deep {
    width: 100%;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 15px;
    background: #ffffff;
    border-radius: 10px;

    .van-button {
      padding-left: 12px;
      padding-right: 12px;
    }

    .van-button__text {
      color: #333333;
      font-size: 12px;
    }
  }

  .btn-item__icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
  }

  .btn-item__name {
    font-size: 16px;
    color: #333333;
    flex: 1;
  }
}

.flex-wrap {
  display: flex;
  align-items: center;

  .flex-item {
    flex: 1;
    margin-right: 15px;
    background: #fff;
    padding: 15px;
    font-size: 16px;
    color: #333333;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    line-height: 1;

    &:last-child {
      margin-right: 0;
    }
  }
}

.box-c {
  margin-top: 15px;

  .block-box {
    width: 100%;
    // height: 68px;
    border-radius: 10px;
    overflow: hidden;
    transform: rotate(0deg);

    .coupon-wrap {
      position: relative;

      .vip-banner {
        width: 100%;
      }

      .renew {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.apply-f {
  bottom: 20px;
  // top: initial !important;

  .f-btn-apply {
    width: 58px;
    height: 58px;
    padding: 0px;
    border-radius: 100px;

    img {
      width: 100%;
      height: 100%;
      display: block;
      margin-bottom: 0;
    }
  }
}
</style>
