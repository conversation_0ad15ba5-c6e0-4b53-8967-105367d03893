<template v-if="status == AppStatus.READY">
  <div class="content-wrap" ref="scrollWrap">
    <traffic-map class="map"></traffic-map>
    <div class="map-mask"></div>
    <div class="content" ref="content">
      <!-- 搜索框 -->
      <van-sticky
        class="mb-15"
        :class="{ 'is-sticky': isSticky }"
        :container="content"
        offset-top="15px"
        @change="stickyChange"
      >
        <div class="search bs-10" @click="pageTo('traffic_search')">
          <span></span>
          <p>搜索附近热门景点、目的地</p>
        </div>
      </van-sticky>
      <!-- 广告位 -->
      <AdsBanner v-if="adsBanners.length" :list="adsBanners" />
      <!-- 高速免费 -->
      <div class="ad-banner mb-15" @click="pageTo('/traffic/freeHighway')">
        <biz-image
          :src="highwayAD"
          :immediate="true"
          fill="cover"
          type="?"
          class="banner"
        >
        </biz-image>
      </div>
      <!-- 政务资源 -->
      <government-affairs></government-affairs>
      <!-- 动态功能区域 -->
      <!-- 2023-12-07 11:35:58 交广领航提审需求：隐藏上门代审功能 -->
      <div
        v-if="!isInJglh"
        class="dynamic-block mb-15 bs-10"
        @click="pageTo('/camera/index')"
      >
        <h2><span>违法行车</span>举报</h2>
        <p>随手拍违法 共创文明交通<span>GO ></span></p>
      </div>
      <!-- 高速路况 -->
      <high-speed @load="apiComplete"></high-speed>
      <!-- 车驾管服务 -->
      <car-service></car-service>
      <!-- 栏目推荐 -->
      <broadcast-column></broadcast-column>
      <!-- 咨询推荐 -->
      <news :type="1" :active="true" :reach-bottom="reachBottomCount"></news>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { AppStatus } from '@/enums';
import { parseJglhURL, pushNativeView, getGeoData } from '@/bridge';
import { mixinAuthRouter } from '@/mixins';
import { isInJglh, isInWeixin, isInWeApp, isProduction } from '@/common/env';
import { toast, dialog } from '@/bus';
import { Sticky } from 'vant';
import { throttle } from 'lodash';
import TrafficMap from './components/Map.vue';
import GovernmentAffairs from './components/GovernmentAffairs.vue';
import HighSpeed from './components/HighSpeed.vue';
import CarService from './components/CarService.vue';
import BroadcastColumn from './components/BroadcastColumn.vue';
import News from './components/News.vue';
import AdsBanner from './components/AdsBanner.vue';

const MenuType = {
  TrafficSearch: 'traffic_search',
};
export default {
  name: 'TrafficAppIndex',
  components: {
    TrafficMap,
    GovernmentAffairs,
    HighSpeed,
    CarService,
    BroadcastColumn,
    News,
    AdsBanner,
    [Sticky.name]: Sticky,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      isInJglh,
      map: null,
      content: null,
      status: AppStatus.LOADING,
      isSticky: false,
      error: '',
      type: 72,
      reachBottomCount: 0,
      scrollTop: 0,
      adsBanners: [
        {
          url: '/savings/card?id=3',
          image: require('@pkg/traffic/images/vip.png'),
        },
        // {
        //   url: '/activity/topic/42',
        //   image: require('@pkg/traffic/images/ads02.png'),
        // },
      ],
      highwayAD: require('@pkg/traffic/images/highwayad.png'),
    };
  },
  watch: {
    $route() {
      const scrollWrap = this.$refs.scrollWrap;
      if (!scrollWrap) return;
      scrollWrap.scrollTop = this.scrollTop;
    },
  },
  mounted() {
    this.init();
    this.content = this.$refs.content;
    this.$refs.scrollWrap.addEventListener(
      'scroll',
      throttle(this.onScroll, 300)
    );
    const payload = {
      key: this.$options.name,
      value: this.$route.path,
    };
    // let name = this.$options.name
    // debugger
    this.addRoute(payload);
  },
  destroyed() {
    this.$refs.scrollWrap &&
      this.$refs.scrollWrap.removeEventListener(
        'scroll',
        throttle(this.onScroll, 300)
      ); // 页面离开后销毁监听事件
  },
  computed: {},
  methods: {
    ...mapActions(['addRoute', 'getAMap']),
    init() {
      // this.status = AppStatus.READY
    },
    stickyChange(isFixed) {
      this.isSticky = isFixed;
    },
    pageTo(item) {
      if (item === MenuType.TrafficSearch) {
        if (!isInJglh) {
          toast().tip('请在最新交广领航APP中打开');
          return;
        }
        pushNativeView({
          id: item,
        });
        return;
      }
      const options = parseJglhURL(item);
      this.$_router_pageTo(options.url, {
        ...options,
        titleBar: false,
        progressBar: false,
      });
    },
    onScroll() {
      this.scrollTop = this.$refs.scrollWrap.scrollTop;
      if (!this.isScrollBottom()) {
        return false;
      }
      this.loadMore();
    },
    loadMore() {
      this.reachBottomCount++;
    },
    isScrollBottom() {
      // 是否滚动到了底部
      // debugger
      this.box = this.$refs.scrollWrap;
      let clientHeight = this.box.clientHeight;
      let scrollTop = this.box.scrollTop;
      let scrollHeight = this.box.scrollHeight;
      if (scrollHeight - scrollTop - clientHeight <= 100) {
        return true;
      } else {
        return false;
      }
    },
    apiComplete() {
      this.status = AppStatus.READY;
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
  },
};
</script>
<style lang="scss">
.mb-15 {
  margin-bottom: 15px;
}
.bs-10 {
  border-radius: 10px;
  overflow: hidden;
}
.card {
  background: #ffffff;
  border-radius: 10px;
  margin-bottom: 15px;
  .card-title {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 0;
    line-height: 1;
    h2 {
      p {
        font-size: 16px;
        font-weight: 800;
        color: #333333;
      }
      span {
        display: block;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-top: 6px;
      }
    }
    .action {
      height: 16px;
      line-height: 16px;
      font-size: 10px;
      font-weight: 500;
      color: #333333;
    }
  }
}
</style>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';
::-webkit-scrollbar {
  display: none;
  background-color: transparent;
}
.content-wrap {
  width: 100%;
  height: 100%;
  overflow: auto;
  // pointer-events: none !important;
}
.map {
  position: fixed;
  top: 0;
  left: 0;
}
.map-mask {
  width: 100%;
  height: 230px;
  // height: calc(230px + constant(safe-area-inset-top));
  // height: calc(230px + env(safe-area-inset-top));
  pointer-events: none;
  background: transparent;
}
.content ::v-deep {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  position: relative;
  z-index: 999;
  background: transparent;
  // pointer-events: auto;
  // touch-action: auto;
  background: linear-gradient(to bottom, #ffffff, #f3f3f3 285px, #f3f3f3);
  border-radius: 10px 10px 0px 0px;
  .search {
    display: flex;
    align-items: center;
    height: 50px;
    padding-left: 15px;
    background: #f3f3f3;
    border-radius: 10px;
    span {
      display: block;
      width: 22px;
      height: 22px;
      background: url(./images/search.png) no-repeat center;
      background-size: 100% 100%;
      margin-right: 10px;
    }
    p {
      font-size: 15px;
      font-weight: 500;
      color: #333333;
    }
  }
  .is-sticky {
    .van-sticky {
      background: #fff;
      // background: linear-gradient(to bottom, #ffffff, #ffffff 60px, #f3f3f3);
      border-radius: 0 0 10px 10px;
      padding: 15px;
      transform: translateY(-16px);
      // transition: background linear .2s;
    }
    .van-sticky--fixed {
      top: calc(15px + constant(safe-area-inset-top)) !important;
      top: calc(15px + env(safe-area-inset-top)) !important;
    }
  }
  .dynamic-block {
    line-height: 1;
    height: 80px;
    padding: 0 15px;
    background: #ffffff url(./images/block.png) no-repeat 230px 0 / 100px 80px;
    h2 {
      padding: 20px 0 10px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      span {
        color: #ff4539;
      }
    }
    p {
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      display: inline-flex;
      align-items: center;
      span {
        line-height: normal;
        margin-left: 10px;
        background: $lh-2022-primary-color;
        border-radius: 9px;
        padding: 2px 6px;
        font-size: 10px;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
  .ad-banner ::v-deep {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    img {
      border-radius: 10px;
    }
  }
}
.news {
  height: 100%;
  margin-top: 0 !important;
}
</style>
