<template>
  <van-popup
    v-model="showPopup"
    round
    closeable
    position="bottom"
    class="popup-sms"
    @close="handleClose"
  >
    <div class="popup-content">
      <img class="popup-icon" src="../images/sms.png" alt="短信求助" />
      <h3 class="popup-title">短信求助</h3>
      <p class="popup-desc">
        根据求助模板如实、准确描述当前情况，平台会及时与您联系并调配救援力量
      </p>
      <van-field
        v-model="phoneNumber"
        label=""
        type="tel"
        class="phone-field"
        maxlength="11"
      />
      <p class="popup-tip">提交接收信息手机号码，即可获取短信求助模板</p>
      <van-button
        block
        type="danger"
        class="popup-btn"
        round
        :loading="loading"
        @click="handleSubmit"
        >立即获取</van-button
      >
      <p class="popup-footer">每日获取次数有限，请留意短信记录</p>
    </div>
  </van-popup>
</template>

<script>
import { Popup, Field, Button } from 'vant';
import { mixinAuthRouter } from '@/mixins';
import { getMessageTemplate } from '../api';
export default {
  name: 'SendSms',
  mixins: [mixinAuthRouter],
  components: {
    [Popup.name]: Popup,
    [Field.name]: Field,
    [Button.name]: Button,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPopup: false, // 控制弹窗显示
      phoneNumber: '', // 默认手机号
      loading: false,
    };
  },
  watch: {
    show(newVal) {
      this.showPopup = newVal;
    },
  },
  mounted() {
    this.phoneNumber = this.$_auth_userInfo.phone || '';
  },
  methods: {
    handleSubmit() {
      this.loading = true;
      getMessageTemplate({
        phone: this.phoneNumber,
      })
        .then(res => {
          // this.$toast('短信求助已发送');
          this.$emit('close');
        })
        .finally(() => {
          this.$toast('短信求助已发送');
          this.$emit('close');
          this.loading = false;
        });
    },
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-sms {
  box-sizing: border-box;
  padding: 20px;
}
.popup-content {
  text-align: center;
}

.popup-icon {
  width: 100px;
  height: 65px;
  margin-bottom: 10px;
}

.popup-title {
  color: #ff5831;
  font-size: 18px;
  font-weight: bold;
}

.popup-desc {
  font-size: 14px;
  color: #333333;
  line-height: 18px;
  margin: 10px 0;
}

.phone-field {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 10px;
  font-size: 16px;
  margin: 10px 0;
}

.popup-tip {
  font-size: 12px;
  color: #616a7b;
}

.popup-btn {
  margin-top: 20px;
  font-size: 18px;
}

.popup-footer {
  font-size: 12px;
  color: #616a7b;
  margin-top: 16px;
}
</style>
