<template>
  <container
    class="theme-gray"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    transition="fade"
  >
    <x-header title="联合会员">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>

    <content-view ref="content" :status="status" @reload="reload">
      <!-- <div class="member">
        <BannerCard
          :user="user"
          :list="banners"
          @change="swiperChange"
        ></BannerCard>
      </div> -->
      <div class="pd-15 mb-48">
        <div class="buy-wrap">
          <biz-image
            :src="combinedImg"
            :immediate="true"
            fill="cover"
            type="?"
            class="combined-img"
          >
          </biz-image>
          <div class="btn-wrap">
            <van-button
              class="buy-btn"
              :class="{ 'van-button--disabled': disabled }"
              type="primary"
              round
              :loading="loading"
              loading-text="提交中"
              @click="createOrder"
              >{{ actionInfo.salePrice }}元立即开通联名会员</van-button
            >
            <!-- <div class="tips">限时开通加送12元回本券</div> -->
          </div>
        </div>
        <RightCard title="云闪付会员权益" class="mb-48">
          <RightUnionPay
            :list="unionRoutineItems"
            @rightsClick="handleRightsClick"
          ></RightUnionPay>
        </RightCard>
        <RightCard title="交广领航会员权益" class="mb-48">
          <RightJglh
            :list="jglhRoutineItems"
            @rightsClick="handleRightsClick"
          ></RightJglh>
        </RightCard>
        <RightCard v-if="actionInfo.useRule" title="使用说明">
          <MemberRule :rule="actionInfo.useRule"></MemberRule>
        </RightCard>
      </div>
    </content-view>
    <!-- 权益弹窗 -->
    <right-pop
      :vip="false"
      :emitParent="true"
      :show="showRight"
      :rights="currnetRoutineItems"
      :currentRights="currentRights"
      :index="currentRightsIndex"
      :vipConfig="currnetVipConfig"
      @close="closePop"
      @buttonClick="handleBuyClick"
    ></right-pop>
  </container>
</template>

<script>
import { formatDate, getAppURL } from '@/utils';
import { callPhone } from '@/bridge';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { getImageURL } from '@/common/image';
import { AppStatus } from '@/enums';
import { isInWeixin, isInJGLH, isInAliApp } from '@/common/env';
import { loading, dialog, toast, back } from '@/bus';
import {
  createUnionMemberOrder,
  getActionInfo,
} from '@pkg/vip-combined/api/index';

import { Icon, Button } from 'vant';
import BannerCard from './_BannerCard.vue';
import RightCard from './_RightCard.vue';
import RightJglh from './_RightJglh.vue';
import RightUnionPay from './_RightUnionPay.vue';
import MemberRule from './_MemberRule.vue';
import RightPop from '@pkg/vip-v2/VIP-Rights/_RightPop.vue';

const RIGHTTYPE = {
  jglh: 'jglh',
  unionPay: 'unionPay',
};
export default {
  name: 'VipCombined',
  components: {
    BannerCard,
    RightCard,
    RightPop,
    RightUnionPay,
    RightJglh,
    MemberRule,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter, mixinShare],
  data() {
    return {
      AppStatus,
      isInWeixin,
      status: AppStatus.LOADING,
      isInAliApp,
      keepAlive: true,
      loading: false,
      banners: [
        {
          url: '/savings/card?id=1',
          image: require('@pkg/vip-combined/assets/images/banner01.png'),
        },
        // {
        //   url: '/activity/topic/42',
        //   image: require('@pkg/traffic/images/ads02.png'),
        // },
      ],
      swiperIndex: 0,
      showRight: false,
      actionInfo: {},
      rightType: RIGHTTYPE.jglh,
      currentRights: {},
      currentRightsIndex: 0,
      combinedImg: require('@pkg/vip-combined/assets/images/combined.png'),
      disabled: true,
    };
  },
  computed: {
    isInMp() {
      return isInWeixin || isInAliApp;
    },
    currnetRoutineItems() {
      if (this.rightType === RIGHTTYPE.jglh) {
        return this.jglhRoutineItems;
      }
      return this.unionRoutineItems;
    },
    jglhRoutineItems() {
      return this.actionInfo.routineItems || [];
    },
    unionRoutineItems() {
      return this.actionInfo.otherPlatformRoutineItems || [];
    },
    // 当前会员配置
    currnetVipConfig() {
      return this.actionInfo;
    },
    user() {
      return this.$_auth_userInfo;
    },
  },
  mounted() {
    console.log('重现加载');
  },
  methods: {
    ...{ formatDate },
    init() {
      if (!this.$route.query.id) {
        toast().tip('活动id为空');
        return;
      }
      Promise.all([getActionInfo(this.$route.query.id)])
        .then(([action]) => {
          this.actionInfo = action || {};
          if (action.memberCardImage) {
            this.banners = [
              {
                ...this.banners[0],
                image: action.memberCardImage,
              },
            ];
          }
          if (action.status === 0) {
            this.disabled = true;
          } else {
            this.disabled = false;
          }
          // 联合会员使用规则
          localStorage.setItem('union_member_rich_text', action.useRule);
          this.status = AppStatus.READY;
          this.share();
        })
        .catch(err => {
          toast().tip(err);
          console.error(err);
          this.status = AppStatus.ERROR;
        });
    },
    handleRightsClick(type, item, index) {
      this.rightType = type;
      this.showRight = true;
      this.currentRights = item;
      this.currentRightsIndex = index;
    },
    handleBuyClick() {
      this.closePop();
      this.createOrder();
    },
    createOrder() {
      if (this.disabled) {
        // toast().tip("");
        return;
      }
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      this.loading = true;
      createUnionMemberOrder({
        unionMemberCategoryId: this.$route.query.id,
        cooperationChannel: this.$route.query.cooperationChannel || 'JGLH',
      })
        .then(res => {
          const { oid, sysOid } = res;
          const paymentInfo = {
            soid: `${sysOid}-${oid}`,
            method: 'push',
            nextPath: '/vip/combined/rule',
          };
          this.$_route_cashierCheckout(paymentInfo);
        })
        .catch(err => {
          toast().tip(err);
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    closePop() {
      this.showRight = false;
    },
    swiperChange(index) {
      this.swiperIndex = index;
    },
    share(action = 'config') {
      // 设置邀请链接分享信息
      let path = this.$route.fullPath;
      const inviteURL = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      });
      const shareInfo = {
        link: inviteURL,
        title: this.actionInfo.shareTitle || '联合会员',
        desc: this.actionInfo.shareContent,
        imgUrl: getImageURL(this.actionInfo.shareImage),
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    onResume() {},
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$vip-color: rgb(230, 197, 126);
.theme-gray ::v-deep {
  background: #221e38;
}
.pd-15 {
  padding: 15px;
}
.mb-48 {
  margin-bottom: 48px;
}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}
.buy-wrap {
  padding: 24px 15px;
  background: linear-gradient(180deg, #ffe9db 0%, #f7bc88 100%);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48px;
  .combined-img {
    display: block;
    width: 100%;
    height: auto;
    margin-bottom: 28px;
  }
  .buy-btn {
    width: 240px;
    height: 44px;
    background: linear-gradient(90deg, #ff5c0d 0%, #ff134f 100%);
    box-shadow: 0px 4px 0px 0px #db1548, inset 0px 0px 5px 0px #ffffff;
    border-radius: 22px 22px 22px 22px;
    font-size: 18px;
    color: #ffffff;
    line-height: 44px;
    text-align: center;
    border: none;
    &:active {
      opacity: 0.8;
    }
  }
}
.block-bottom {
  margin-top: 25px;
  padding: 15px;
  overflow: hidden;
  position: relative;
  line-height: 1;
}
.btn-wrap {
  position: relative;
  .tips {
    background: linear-gradient(90deg, #ffe9b1 0%, #fee3cf 100%),
      linear-gradient(270deg, #ffe9db 0%, #ffe4b6 100%);
    border-radius: 9px 9px 9px 0px;
    font-size: 10px;
    color: #793011;
    padding: 0 8px;
    height: 18px;
    line-height: 18px;
    position: absolute;
    left: 50%;
    top: -11px;
    word-break: keep-all;
  }
}
</style>
