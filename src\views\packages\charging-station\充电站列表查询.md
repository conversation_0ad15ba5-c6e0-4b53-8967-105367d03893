## 多条件查询充电站

**接口地址**:`/api/chargingstations/query`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**:根据多种条件组合查询充电站列表

**请求示例**:

```javascript
	{
	"benefits": [],
	"businessHoursType": "",
	"chargingMethods": [],
	"city": "",
	"district": "",
	"highwayMode": "",
	"keyword": "",
	"latitude": 0,
	"longitude": 0,
	"maxPower": 0,
	"maxVoltage": 0,
	"minPower": 0,
	"minVoltage": 0,
	"operationType": "",
	"page": 1,
	"pageSize": 10,
	"parkingFeeType": "",
	"parkingTypes": [],
	"province": "",
	"services": [],
	"sortDirection": "",
	"sortField": "",
	"stationType": "",
	"status": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称                      | 参数说明                             | in   | 是否必须 | 数据类型        | schema          |
| ----------------------------- | ------------------------------------ | ---- | -------- | --------------- | --------------- |
| queryDTO                      | 充电站查询DTO                        | body | true     | StationQueryDTO | StationQueryDTO |
| &emsp;&emsp;benefits          | 权益                                 |      | false    | array           | string          |
| &emsp;&emsp;businessHoursType | 营业时间类型                         |      | false    | string          |                 |
| &emsp;&emsp;chargingMethods   | 充电方式                             |      | false    | array           | string          |
| &emsp;&emsp;city              | 城市                                 |      | false    | string          |                 |
| &emsp;&emsp;district          | 区/县                                |      | false    | string          |                 |
| &emsp;&emsp;highwayMode       | 高速模式                             |      | false    | string          |                 |
| &emsp;&emsp;keyword           | 搜索关键词（名称或地址）             |      | false    | string          |                 |
| &emsp;&emsp;latitude          | 纬度（用于计算距离）                 |      | false    | number          |                 |
| &emsp;&emsp;longitude         | 经度（用于计算距离）                 |      | false    | number          |                 |
| &emsp;&emsp;maxPower          | 最大功率（千瓦）查询条件             |      | false    | integer(int32)  |                 |
| &emsp;&emsp;maxVoltage        | 最大电压（伏特）查询条件             |      | false    | integer(int32)  |                 |
| &emsp;&emsp;minPower          | 最小功率（千瓦）查询条件             |      | false    | integer(int32)  |                 |
| &emsp;&emsp;minVoltage        | 最小电压（伏特）查询条件             |      | false    | integer(int32)  |                 |
| &emsp;&emsp;operationType     | 运营类型                             |      | false    | string          |                 |
| &emsp;&emsp;page              | 页码                                 |      | false    | integer(int32)  |                 |
| &emsp;&emsp;pageSize          | 每页大小                             |      | false    | integer(int32)  |                 |
| &emsp;&emsp;parkingFeeType    | 停车费类型                           |      | false    | string          |                 |
| &emsp;&emsp;parkingTypes      | 停车场类型                           |      | false    | array           | string          |
| &emsp;&emsp;province          | 省份                                 |      | false    | string          |                 |
| &emsp;&emsp;services          | 电站服务                             |      | false    | array           | string          |
| &emsp;&emsp;sortDirection     | 排序方向（asc/desc）,可用值:asc,desc |      | false    | string          |                 |
| &emsp;&emsp;sortField         | 排序字段                             |      | false    | string          |                 |
| &emsp;&emsp;stationType       | 电站类型                             |      | false    | string          |                 |
| &emsp;&emsp;status            | 电站状态                             |      | false    | string          |                 |

**响应状态**:

| 状态码 | 说明         | schema                            |
| ------ | ------------ | --------------------------------- |
| 200    | OK           | Result«PageResult«StationVO»» |
| 201    | Created      |                                   |
| 401    | Unauthorized |                                   |
| 403    | Forbidden    |                                   |
| 404    | Not Found    |                                   |

**响应参数**:

| 参数名称                                      | 参数说明                                | 类型                    | schema                  |
| --------------------------------------------- | --------------------------------------- | ----------------------- | ----------------------- |
| code                                          |                                         | integer(int32)          | integer(int32)          |
| data                                          |                                         | PageResult«StationVO» | PageResult«StationVO» |
| &emsp;&emsp;list                              | 当前页数据列表                          | array                   | StationVO               |
| &emsp;&emsp;&emsp;&emsp;acCount               | 交流充电桩数量                          | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;address               | 地址                                    | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;availableChargerCount | 可用充电桩数量                          | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;benefits              | 权益                                    | array                   | string                  |
| &emsp;&emsp;&emsp;&emsp;businessHoursDesc     | 具体营业时间描述                        | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;businessHoursType     | 营业时间类型                            | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;chargerCount          | 充电桩数量                              | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;chargingMethods       | 充电方式                                | array                   | string                  |
| &emsp;&emsp;&emsp;&emsp;city                  | 城市                                    | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;dcCount               | 直流充电桩数量                          | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;delStatus             | 删除状态                                | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;distance              | 距离（公里）                            | number(double)          |                         |
| &emsp;&emsp;&emsp;&emsp;district              | 区/县                                   | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;highwayMode           | 高速模式                                | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;id                    | 主键ID                                  | integer(int64)          |                         |
| &emsp;&emsp;&emsp;&emsp;images                | 充电站图片URL列表（多张图片用逗号分隔） | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;latitude              | 纬度                                    | number                  |                         |
| &emsp;&emsp;&emsp;&emsp;longitude             | 经度                                    | number                  |                         |
| &emsp;&emsp;&emsp;&emsp;maxPower              | 最大功率（千瓦）                        | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;maxVoltage            | 最大电压（伏特）                        | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;minPower              | 最小功率（千瓦）                        | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;minVoltage            | 最小电压（伏特）                        | integer(int32)          |                         |
| &emsp;&emsp;&emsp;&emsp;name                  | 充电站名称                              | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;operationType         | 运营类型                                | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;parkingFeeType        | 停车费类型                              | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;parkingFeeTypeDesc    | 停车费类型详情描述                      | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;parkingTypes          | 停车场类型                              | array                   | string                  |
| &emsp;&emsp;&emsp;&emsp;pricePerKwh           | 充电单价(元/度)                         | number                  |                         |
| &emsp;&emsp;&emsp;&emsp;province              | 省份                                    | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;rating                | 评分                                    | number                  |                         |
| &emsp;&emsp;&emsp;&emsp;services              | 电站服务                                | array                   | string                  |
| &emsp;&emsp;&emsp;&emsp;stationType           | 电站类型                                | string                  |                         |
| &emsp;&emsp;&emsp;&emsp;status                | 电站状态                                | string                  |                         |
| &emsp;&emsp;pageNum                           | 当前页码                                | integer(int32)          |                         |
| &emsp;&emsp;pageSize                          | 每页大小                                | integer(int32)          |                         |
| &emsp;&emsp;pages                             | 总页数                                  | integer(int32)          |                         |
| &emsp;&emsp;total                             | 总记录数                                | integer(int64)          |                         |
| msg                                           |                                         | string                  |                         |
| success                                       |                                         | boolean                 |                         |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"acCount": 0,
				"address": "",
				"availableChargerCount": 0,
				"benefits": [],
				"businessHoursDesc": "",
				"businessHoursType": "",
				"chargerCount": 0,
				"chargingMethods": [],
				"city": "",
				"dcCount": 0,
				"delStatus": 0,
				"distance": 0,
				"district": "",
				"highwayMode": "",
				"id": 0,
				"images": "",
				"latitude": 0,
				"longitude": 0,
				"maxPower": 0,
				"maxVoltage": 0,
				"minPower": 0,
				"minVoltage": 0,
				"name": "",
				"operationType": "",
				"parkingFeeType": "",
				"parkingFeeTypeDesc": "",
				"parkingTypes": [],
				"pricePerKwh": 0,
				"province": "",
				"rating": 0,
				"services": [],
				"stationType": "",
				"status": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"pages": 0,
		"total": 0
	},
	"msg": "",
	"success": true
}
```
