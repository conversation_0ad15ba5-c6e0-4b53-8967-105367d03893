<style lang="scss" scoped>
  .lh-input-search {
    // background: white;
    background: #ECECEC;
    padding: 1px;
    line-height: 1;
    border-radius: 2px;
    padding: 6px 0;
    overflow: hidden;
    color: #b1b1b1;
    position: relative;
    flex: 1;
    display: flex;
    margin: 0 18%;
    font-size: 13px;
    align-items: center;
    .icon-search {
      font-size: 14px;
      margin: 0 3px 0 5px;
      position: relative;
    }
    .input-search-content {
      flex:1;
      display: flex;
      position: relative;
      /* iOS端input获取焦点，input会有高亮特殊样式，加一个浮层可规避掉 */
      /* iOS12，input获取焦点后有些设备上不显示光标，暂时去掉 */
      /* &::after {
        content:'';
        position:absolute;
        width:100%;
        height:100%;
        top:0;
        left:0;
        z-index:1;
      } */
      &.input-box-acitve::after {
        display:none;
      }
    }
    .input {
      padding: 1px;
      border: 0;
      box-sizing: border-box;
      width:100%;
      font-size: 14px;
      background: #ECECEC;
    }
    .search-clear {
      z-index: 2;
      text-align:center;
      font-size: 16px;
      margin-right: 6px;
      border-radius: 50%;
      position: absolute;
      right: 0;
      &::before{
        content: "\e602";
        font-family:iconfont;
        margin:0 2px 0 5px;
      }
    }
    .input:focus {
      outline:none;
    }
  }
  // 解决ios15适配 input框出现默认放大镜的问题
  input[type="search"]{
    -webkit-appearance:none;
  }
  // [type="search"]::-webkit-search-decoration {
  //   display: none;
  // }
  // input::-webkit-search-cancel-button {
  //   display: none;
  // }

  input[type="search"]::-webkit-search-cancel-button{
    -webkit-appearance: none;
    display: none;
  }
  input[type="search"]::placeholder {
    font-size: 13px;
  }
</style>

<template>
  <div class="lh-input-search">
    <svg class="icon-search">
      <use xlink:href="#icon-search"></use>
    </svg>
    <div class="input-search-content" @click="focusSearchInput" :class="{'input-box-active': searchInputting}">
      <input ref="search" type="search" @blur="onSearchInputBlur" v-focus autofocus class="input" :placeholder="placeholder" v-model="word">
    </div>
    <span v-show="word" @click="clearSearchInput" class="search-clear"></span>
  </div>
</template>

<script>
import debounce from 'lodash/debounce';

let debounceSearch;

export default {
  name: 'input-search',
  props: {
    placeholder: {
      type: String,
      default: '请输入',
    },
    defaultValue: {
      type: String,
      default: '',
    },
    autoFocus: {
      type: Boolean,
      default: true,
    }
  },
  directives: {
    focus: {
      inserted(el) {
        // console.warn('input focus...', el)
        el.focus();
      },
    },
  },
  data() {
    return {
      searchInputting: false,
      word: '',
      sw: '',
    };
  },
  watch: {
    word(val) {
      debounceSearch(val);
    },
    defaultValue(val) {
      console.log('default value change:', val);
      this.setValue(val);
    }
  },
  mounted() {
    console.log('input search mounted...')
    this.init();
  },
  methods: {
    init() {
      debounceSearch = debounce(sw => {
        this.doChange(sw);
      }, 200);
      if (this.defaultValue) {
        console.log('setDefaultValue');
        this.setValue(this.defaultValue);
      }
      if (this.autoFocus) {
        setTimeout(() => {
          this.focusSearchInput();
        }, 100)
      }
      // this.initEvents();
    },
    initEvents() {
      // 中文输入法v-model composition 同步问题已在vue2.4.0中解决，
      // 解决方式参考vue源代码
      const el = this.$refs.search;
      const debounceSearch = debounce(sw => {
        if (this.word) {
          this.sw = sw;
          this.doChange(sw);
        } else {
          this.sw = '';
          this.doChange('');
        }
      }, 200)

      let cpLock = false
      el.addEventListener('compositionstart', e => {
        cpLock = true
      })
      el.addEventListener('compositionend', e => {
        cpLock = false
        debounceSearch(el.value)
      })
      el.addEventListener('input', e => {
        if (!cpLock) debounceSearch(el.value)
      })
    },
    focusSearchInput() {
      this.$nextTick(() => {
        this.searchInputting = true;
        this.$refs.search.focus();
      })
    },
    blurSearchInput() {
      this.$refs.search.blur();
    },
    onSearchInputBlur() {
      this.searchInputting = false;
    },
    clearSearchInput(focus) {
      this.clear(focus)
      this.$emit('clear');
      this.doChange('');
    },
    doChange(sw) {
      this.$emit('input', sw);
      this.$emit('change', sw);
    },
    setValue(value) {
      this.word = value;
      this.sw = value;
    },
    clear(focus = true) {
      this.sw = '';
      this.word = '';
      if (focus) {
        this.$refs.search.focus();
      }
    },
  }
}
</script>
