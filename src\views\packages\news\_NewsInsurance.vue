<template>
  <page-loader class="page-news" :status="status" @reload="reload">
    <slider
      v-if="newsTabs.length"
      ref="slider"
      :slides="newsTabs"
      :options="{
        touchStartPreventDefault: false,
        autoplay: false,
        touchAngle: 30,
        threshold: 50,
      }"
      :pagination="false"
      :allow-empty="true"
      :initialSlide="initialSlide"
      value-mode="index"
      v-model="slider"
      @change="onSlide"
    >
      <template v-slot:item="{ item, index }">
        <!-- <div class="swiper-slide"> -->
        <news-panel-item
          :type="item.id"
          :active="item.id === newsType"
          :index="index"
          :sw="sw"
          :searchIndex="searchIndex"
          :searchTime="searchTime"
        ></news-panel-item>
        <!-- </div> -->
      </template>
    </slider>
    <div v-else class="weui-msg">
      <div class="weui-msg__icon-area">
        <i :class="['weui-icon-info', 'weui-icon_msg']"></i>
      </div>
      <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">暂无新闻</h2>
        <p class="weui-msg__desc">如有疑问，请联系客服</p>
      </div>
    </div>
  </page-loader>
</template>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color: #ebebeb;

$menu-border-color: #dadada;
.page-news {
  display: flex;
  flex-direction: column;
}
.page-news ::v-deep {
  flex: 1;
  overflow-y: hidden;
  .swiper-container {
    // margin-top: 10px;
    display: flex;
    flex: 1;
    min-height: calc(100% - 98px); /* 临时解决 ios9 高度无法撑满父容器*/
    height: 100%;
  }
  .swiper-wrapper {
    flex: 1;
    height: auto;
  }
  .swiper-slide {
    display: flex;
    height: inherit;
    .content-wrapper,
    .page-content {
      margin-top: 0 !important;
    }
  }
  .news-panel {
    padding: 5px 10px;
    background: white;
    min-height: 100px;
  }
}
.news-tabs {
  background: white;
  box-shadow: 0 0 0 0 #dcdcdc; /* px */
  z-index: 1;
}
.error {
  text-align: center;
  padding: 10px;
  margin: 20px;
  &::before {
    content: '\E64f';
    font-family: iconfont;
    display: block;
    font-size: 38px;
    color: #3a3a3a;
  }
}
.error {
  margin-top: 30%;
}
</style>
<script>
import { formatDate, getAbsolutePath } from '@/utils';
import { toast, dialog, loading } from '@/bus';
import { getNewsCategoriesInsurance } from '@/api';
import { AppStatus, NativeView } from '@/enums';
import { Tabs } from '@/components';
import NewsPanelItem from './NewsPanelItemInsurance.vue';
import NewsNav from './_NewsNav.vue';
import Slider from '@/components/Slider/index2.vue';
import { setTitle } from '@/bridge';

function saveNewsCategory(res) {
  localStorage.setItem('_lhspa_news_category', JSON.stringify(res));
}

function getNewsCategory() {
  const cachedCategory = JSON.parse(localStorage.getItem('_news_category'));
  // console.info('cachedCategory:', cachedCategory);
  return cachedCategory || null;
}

function getCategoryFromCache() {
  return new Promise((resolve, reject) => {
    resolve(getNewsCategory());
  });
}

const PAGE_SIZE = 10;
export default {
  name: 'news-list',
  props: {
    sw: String,
    searchIndex: [String, Number],
    searchTime: [Number],
    tabs: Array,
  },
  components: {
    Tabs,
    NewsPanelItem,
    Slider,
    NewsNav,
  },
  mounted() {
    // setTitle('交广新闻');
    this.init();
  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      home: {
        news: [],
        types: [],
      },
      newsList: {},
      offsetTime: Date.now(),
      slider: 0,
      currentTabIndex: 0,
      initialSlide: 0,
      refreshAction: 1,
      readList: {},
    };
  },
  computed: {
    newsType() {
      return this.newsTabs[this.currentTabIndex].id;
    },
    newsTabs() {
      return this.tabs || [];
    },
  },
  watch: {
    tabs(val) {
      if (val.length) {
        this.init();
      }
    },
  },
  methods: {
    test() {
      console.log('init...');
    },
    init() {
      // return this.getNewsCategory();
      if (this.newsTabs.length) {
        this.currentTabIndex = 0;
      }
      if (this.$route.query.id) {
        let index = this.newsTabs.findIndex(
          item => item.id == this.$route.query.id
        );
        if (index > -1) {
          this.initialSlide = index;
          this.currentTabIndex = index;
        }
      }
      if (this.newsTabs.length) {
        this.status = AppStatus.READY;
      }
    },
    // getNewsCategory() {
    //   return getNewsCategoriesInsurance()
    //     .then(res => {
    //       this.home.types = res;
    //       // saveNewsCategory(res);
    //       if (res.length) {
    //         this.currentTabIndex = 0;
    //         this.$nextTick(() => {
    //           this.newsTabScrollable = res.length > 4;
    //         });
    //       }
    //       if (this.$route.query.id) {
    //         let index = this.home.types.findIndex(
    //           item => item.id == this.$route.query.id
    //         );
    //         if (index > -1) {
    //           this.initialSlide = index;
    //           this.currentTabIndex = index;
    //         }
    //       }
    //       this.status = AppStatus.READY;
    //     })
    //     .catch(e => {
    //       console.error(e);
    //       this.status = AppStatus.ERROR;
    //     });
    // },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    switchType(index) {
      // debugger
      this.currentTabIndex = index;
      this.$refs.slider.slideTo(index);
    },
    slideNews(index) {
      // debugger
      this.currentTabIndex = index;
      this.$refs.slider.slideTo(index);
    },
    onResume() {
      console.log('home resume...');
    },
    onSlide(e) {
      console.log('onSlide:', ...arguments);
      // this.newsType = this.slider.id;
      this.$emit('slide', e);
    },
  },
};
</script>
