<template v-if="status == AppStatus.READY">
  <div class="map-wrap">
    <div class="map-container" id="map"></div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { AppStatus } from '@/enums';
import { getGeoData, pushNativeView } from '@/bridge';
import { dialog } from '@/bus';
import AMapLoader from '@amap/amap-jsapi-loader';

export default {
  name: 'Map',
  props: {
    // locations: Object
  },
  data() {
    return {
      AppStatus,
      map: null,
      status: AppStatus.LOADING,
      error: '',
      locations: {
        name: '',
        address: '河南交通广播',
        latitude: 34.7676,
        longitude: 113.683,
      },
    };
  },
  watch: {
    // locations: {
    //   handler: function (val, oldVal) {
    //     // this.locations = val
    //     if (this.map) {
    //       this.map.setZoomAndCenter(16, [val.longitude, val.latitude]);
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    this.init();
  },
  computed: {},
  methods: {
    // ...mapActions(['getAMap']),
    async init() {
      await this.getLocation();
      let locationInfo = {
        lat: this.locations.latitude,
        lng: this.locations.longitude,
      };
      AMapLoader.load({
        key: 'bcbc51d610133a0fdb31ba5dfcea9752', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        // plugins: ['AMap.Scale', 'AMap.ToolBar'],
      })
        .then(AMap => {
          // map = new AMap.Map('container');
          let center = {
            lng: this.locations.longitude,
            lat: this.locations.latitude,
          };
          this.initMap(AMap, [center.lng, center.latitude], locationInfo);
        })
        .catch(e => {
          console.log(e);
        });
    },
    async getLocation() {
      const geo = await getGeoData();
      this.locations = Object.assign(this.locations, geo);
    },
    initMap(AMap, center, location) {
      if (!AMap) {
        dialog().alert('AMap加载出错');
      }
      let map = AMap;
      const userPosition = [location.lng || 113.683, location.lat || 34.7676];
      try {
        map = new AMap.Map('map', {
          resizeEnable: true,
          zoom: 16,
          center: userPosition,
          // dragEnable: false,
          // zoomEnable: false,
        });
        this.map = AMap;
        window.map = map;
      } catch (error) {
        // dialog().alert('AMap加载出错')
        // this.init();
      }
      // 实时路况图层
      let trafficLayer = new AMap.TileLayer.Traffic({
        zIndex: 10,
        zooms: [7, 22],
      });

      trafficLayer.setMap(map);

      // 根据覆盖物范围调整视野,如果需要保证所有覆盖物都在视野范围内， 需要将地图调整到合适的缩放等级和中心点
      // map.setFitView();
      // map.setZoomAndCenter(16, userPosition);
      // map.panBy(0, -100)
      // shopInfo.open(map, userPosition);
      map.on('complete', function () {
        // 地图图块加载完成后触发
        map.add(
          new AMap.Marker({
            map,
            icon: 'http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
            position: [location.lng, location.lat],
            anchor: 'bottom-center', // 设置锚点方位
          })
        );
        this.status = AppStatus.READY;
      });
    },
    toAppMap() {
      // if(){
      pushNativeView({
        id: 'traffic_map',
      });

      // }
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      // this.init();
    },
  },
  beforeDestroy() {
    // 销毁地图，并清空地图容器
    window.map && window.map.destroy();
    // 地图对象赋值为null
    window.map = null;
    // 清除地图容器的 DOM 元素
    document.getElementById('map') && document.getElementById('map').remove();
  },
};
</script>
<style lang="scss" scope>
$arrow-height: 10px;
// .amap-logo {
//   display: none;
//   opacity: 0 !important;
// }
// .amap-copyright {
//   opacity: 0;
// }
.map-container {
  width: 100%;
  height: calc(40vh + constant(safe-area-inset-top));
  height: calc(40vh + env(safe-area-inset-top));
  margin: 0px;
}
.map-wrap {
  width: 100%;
  position: relative;
  background: #f3f3f3;
  .amap-icon {
    width: 20px;
    height: 31px;
    background: url(~@/assets/images/amap/marker.png) center center no-repeat
      transparent;
    background-size: cover;
    img {
      visibility: hidden;
    }
  }
}
</style>
