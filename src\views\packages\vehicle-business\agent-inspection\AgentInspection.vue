<template>
  <container
    class="agent-inspection"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    mode="fullscreen"
    :keep-alive="keepAlive"
  >
    <x-header title="代驾审车" :fullscreen-config="headerFullscreenConfig">
      <x-button slot="left" type="back"></x-button>
    </x-header>

    <content-view
      class="inspection-wrap"
      :status="status"
      @reload="reload"
      @scroll="onPageScroll"
    >
      <template v-if="status == AppStatus.READY">
        <header class="inspection-header">
          <h1>代驾审车</h1>
          <p>足不出户，完成审车</p>
          <biz-image
            class="inspection-step"
            src="../assets/images/step-smds.png"
            type="?"
            fill="fill"
            immediate
          >
          </biz-image>
        </header>
        <Card title="预约信息">
          <InspectionForm
            :type="VehicleBusinessType.AGENT_INSPECTION"
            :cars="actions"
            @pop="hideBottom"
            @switchCar="showPopover = true"
            v-model="formData"
          ></InspectionForm>
        </Card>
        <div v-if="price > 0" class="cost-row">
          <div class="row">
            <span class="content-attr">审车费用</span>
            <div>
              <span class="rmb">{{ price }}</span>
            </div>
          </div>
        </div>

        <Card title="预约须知">
          <ul class="tip-list">
            <li>本服务仅支持郑州市区四环内，超出四环的请勿下单</li>
            <li>
              下单完成，请保持手机畅通，会有专人与您联系，确认审车时间、审车材料等注意事项
            </li>
            <li>
              工作人员在约定时间到达取车，需要您出示订单消费码，当天完成审车并送回车辆
            </li>
            <li>
              如遇约定好时间地点后，工作人员到达后联系不上客户的情况，订单不予退款！
            </li>
            <li>本服务仅适用7座（含）以内的私家车，不适用面包车</li>
            <li>本服务由第三方机构提供</li>
          </ul>
        </Card>

        <!-- <vip-banner :vip="$_auth_isCarVip" @click="$_route_vipPage"></vip-banner> -->

        <ServiceBottom></ServiceBottom>
        <!-- <layout-bottom v-if="formValidation && showBottom" slot="foot" :price="service.price"
          :vip-price="service.vipPrice" :vip="$_auth_isCarVip" @go="submit">
        </layout-bottom> -->
        <div class="button-sp-area">
          <van-button
            class="item-bar__button"
            block
            round
            :loading="loading"
            loading-text="提交中"
            type="danger"
            @click="submit"
          >
            <div class="submit-text-wrap">
              <p>先审后付</p>
              <span>审车后再付款 立即预约</span>
            </div>
          </van-button>
        </div>
        <van-action-sheet
          v-model="showPopover"
          :actions="actions"
          cancel-text="取消"
          description="选择您的车辆"
          close-on-click-action
          @select="onSelect"
          @cancel="showPopover = false"
        />
      </template>
    </content-view>
  </container>
</template>

<script>
import { mixinAuthRouter, mixinShare, mixinOrder } from '@/mixins';
import { AppStatus, OrderBizType } from '@/enums';
import FormValidater from '@/model/FormValidater';
import { loading, dialog, toast } from '@/bus';
import { isAndroid, isInWeixin, isInJGLH } from '@/common/env';
import { VehicleBusinessType } from '@pkg/vehicle-business/enums';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import {
  getVehicleBusinessInfo,
  createOrder4AgentVehicleInspectionForCCB,
  getCarLicenseList,
  saveReservationInspection,
} from '@pkg/vehicle-business/api';
import {
  onCarOwnerConfirmed,
  onBusinessServerConfirmed,
} from '@pkg/vehicle-business/bus';

import VipBanner from '@pkg/vehicle-business/components/_VipBanner.vue';
import LayoutBottom from '@pkg/vehicle-business/components/_LayoutBottom.vue';
import Card from '@pkg/vehicle-business/components/Card.vue';
import ServiceBottom from '@pkg/vehicle-business/components/ServiceBottom.vue';
import InspectionForm from '@pkg/vehicle-business/components/InspectionForm.vue';
import { Button, ActionSheet, Dialog } from 'vant';

/**
 * 上门代审首页
 */
export default {
  name: 'AgentInspection',
  components: {
    'layout-bottom': LayoutBottom,
    VipBanner,
    Card,
    ServiceBottom,
    InspectionForm,
    [ActionSheet.name]: ActionSheet,
    [Dialog.name]: Dialog,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter, mixinShare, mixinOrder],
  data() {
    return {
      VehicleBusinessType,
      // 分享信息，mixinShare自动读取并设置
      $_share_info: {
        path: '/vehicle-business',
        title: '交管车务',
      },
      AppStatus,
      status: AppStatus.LOADING,
      keepAlive: true,
      scrollTop: 0,
      category: this.$route.query.category,
      info: null,
      showBottom: true,
      formData: {},
      showPopover: false,
      // 通过 actions 属性来定义菜单选项
      actions: [],
      loading: false,
    };
  },
  computed: {
    // 全屏模式下 header配置
    headerFullscreenConfig() {
      return {
        // backgroundColor: 'rgba(56, 142, 253, 0)',
        backgroundColor: 'rgba(255, 255, 255, 0)',
        fullscreenHeight: window.innerWidth,
        scrolled: this.scrollTop,
      };
    },
    price() {
      if (this.$_auth_isCarVip) return this.service.vipPrice || 0;
      return this.service.price || 0;
    },
    service() {
      const server = this.formData.server;
      if (server) {
        return {
          price: server.price,
          vipPrice: server.vipPrice,
        };
      }
      return {
        // price: this.info.price,
        // vipPrice: this.info.vipPrice,
        price: 0,
        vipPrice: 0,
      };
    },
    form2() {
      const server = this.formData.server;
      return {
        bid: server ? server.id : null, // 商家id
        joinVip: this.formData.joinVip, // 是否勾选开通会员
        // name: owner ? owner.name : null, // 车主姓名
        name: '', // 车主姓名  2022-02-24 修改，运营要求去除车主姓名、车主身份证，因后台校验车主姓名，故传空字符串
        phone: this.formData.phone || null, // 手机号
        carno: this.formData.carno || null, // 车牌号
        carModel: this.formData.carModel || null, // 车型信息
        // cardId: owner ? owner.cardId : null, // 身份证号
        appointmentTime: this.formData.appointmentTime || null, // 预约时间（时间戳）
        firstRegistDate: this.formData.firstRegistDate,
        categoryId: 6, // 2025-07-03，新增预约审车需要，代办审车固定值
      };
    },
    formValidation() {
      if (
        !this.formData.carno ||
        !(this.formData.server || {}).id ||
        !this.formData.phone
      ) {
        return false;
      } else {
        return true;
      }
    },
  },
  mounted() {},
  methods: {
    onSelect(val) {
      let car = { ...val };
      let year = car.registDate.substring(0, 4);
      let month = car.registDate.substring(4, 6);
      let day = car.registDate.substring(6, 8);
      if (year && month && day) {
        car.registDate = `${year}-${month}-${day}`;
      } else {
        car.registDate = '';
      }
      this.formData = {
        ...this.formData,
        name: car.owner, // 车主姓名
        carno: car.carNo, // 车牌号
        firstRegistDate: car.registDate, // 初次登记日期
      };
    },
    hideBottom(flag) {
      this.showBottom = flag;
    },
    initPageData() {
      Promise.all([
        getVehicleBusinessInfo(VehicleBusinessType.AGENT_INSPECTION),
        getCarLicenseList(),
      ])
        .then(([res, license]) => {
          this.info = res;
          if (license && license.length == 1) {
            let storedCar = { ...license[0] };
            let year = storedCar.registDate.substring(0, 4);
            let month = storedCar.registDate.substring(4, 6);
            let day = storedCar.registDate.substring(6, 8);
            if (year && month && day) {
              storedCar.registDate = `${year}-${month}-${day}`;
            } else {
              storedCar.registDate = '';
            }
            this.formData = {
              ...this.formData,
              name: storedCar.owner, // 车主姓名
              carno: storedCar.carNo, // 车牌号
              firstRegistDate: storedCar.registDate, // 初次登记日期
            };
          }
          if (license && license.length > 1) {
            this.actions = license.map(item => {
              return {
                ...item,
                name: item.carNo,
              };
            });
            setTimeout(() => {
              this.showPopover = true;
            }, 1000);
          }
          this.status = AppStatus.READY;
        })
        .catch(e => {
          toast().tip(e);
          this.status = AppStatus.ERROR;
        });
      this.$_auth_checkSession();
    },
    init() {
      return this.initPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      // this.initPageData();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    doSubmit() {
      this.$_auth_checkSession()
        .then(res => {
          res.ok ? this.submit() : this.$_auth_login();
        })
        .catch(e => {
          e && dialog().alert(e);
        });
    },
    validateForm() {
      return new Promise((resolve, reject) => {
        if (!this.formData.carno) reject('请填写车牌号码！');
        if (!/^1\d{10}$/i.test(this.formData.phone)) reject('请填写手机号码！');
        if (!this.formData.server.id) reject('请选择服务公司！');
        if (!this.formData.firstRegistDate) {
          reject('请选择初次登记日期！');
        }
        resolve();
      });
    },
    async submit(checked) {
      if (this.checkTimeTemp()) return;
      this.formData.joinVip = checked;
      return this.category === 'ccb'
        ? this.submitOrderForCCB()
        : this.submitReservation();
      // : this.submitOrder();
    },
    async submitReservation() {
      try {
        await this.validateForm(this.form2);
        const form = this.form2;
        // 添加合作渠道字段
        if (this.$route.query.cooperationChannel) {
          form.cooperationChannel = this.$route.query.cooperationChannel;
        }
        loading(true, '正在提交...');
        this.loading = true;
        saveReservationInspection(form).then(res => {
          loading(false);
          this.loading = false;
          Dialog.alert({
            title: '预约成功',
            message: '车务小秘书会及时联系您确认相关信息，请保持手机畅通！',
            theme: 'round-button',
          }).then(() => {
            // on close
            let query = {};
            if (this.$route.query.cooperationChannel) {
              query.cooperationChannel = this.$route.query.cooperationChannel;
            }
            this.$_router_replace({ name: 'VehicleOrderReserve', query });
          });
        });
      } catch (err) {
        this.loading = false;
        loading(false);
        err &&
          dialog().alert(err, {
            title: '提示',
          });
      }
    },
    // 建行上门待审下单
    async submitOrderForCCB() {
      try {
        await this.validateForm();
        loading(true);
        const res = await createOrder4AgentVehicleInspectionForCCB(this.form2);
        this.$_route_cashierCheckout({
          soid: res.sysOid,
          method: 'replace',
          type: OrderBizType.AgentVehicleInspection,
          nextPath: `/vehicle-business/order/result/${res.oid}`,
        });
        loading(true);
      } catch (e) {
        e && dialog('提示').alert(e);
      } finally {
        loading(false);
      }
    },
    async submitOrder() {
      try {
        await this.validateForm();
        const form = this.form2;
        this.$_order_preSubmit(
          {
            type: OrderBizType.AgentVehicleInspection,
            resultUrlRender(oid) {
              return `/vehicle-business/order/result/${oid}`;
            },
            form,
            // 审车订单查询优惠券信息参数
            ticketQuery: {
              category: this.$_order_TicketCategory.AgentCarInspection,
              rid: form.bid,
            },
            // 合作渠道——微信社群营销
            cooperationChannel: this.$route.query.cooperationChannel,
            // 确认订单页面订单信息
            order: {
              title: this.formData.server.name,
              id: 0,
              name: '上门代审',
              price: this.service.price,
              mprice: this.service.price,
              vipPrice: this.service.vipPrice || this.service.price,
            },
          },
          'replace'
        );
        this.keepAlive = false;
      } catch (err) {
        err &&
          dialog().alert(err, {
            title: '提示',
          });
      } finally {
        loading(false);
      }
    },
    checkTimeTemp() {
      const { available, message } = checkServiceStatus(
        ServiceEnum.CAR_INSPECTION
      );
      if (!available) {
        dialog().alert(message, {
          title: '通知',
        });
      }
      return !available;
    },
    onPageScroll(top) {
      this.scrollTop = top;
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
@import '@pkg/vehicle-business/assets/styles/common.scss';

.inspection-header {
  background: url(~@pkg/vehicle-business/assets/images/page-head-smds.png)
    no-repeat 242px 80px;
  background-position-y: calc(80px + constant(safe-area-inset-top));
  background-position-y: calc(80px + env(safe-area-inset-top));
  background-size: 100px 100px;
}
.agent-inspection {
  .vip-banner {
    height: 40px;
    margin: 0 0 10px 0;
  }

  .cost-row {
    background: #ffffff;
    border-radius: 10px;
    padding: 10px 15px;
    margin-bottom: 15px;

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .row-tip {
      line-height: 1.5;
    }

    .content-attr {
      font-size: 16px;
      color: #111111;
    }

    .rmb {
      font-size: 15px;
      font-weight: bold;
      color: #fd4925;

      &::before {
        font-size: 10px !important;
      }
    }
  }

  .button-sp-area {
    padding: 0 15px 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;

    .item-bar__button {
      font-size: 18px;
      flex: 0 0 auto;
      height: 54px;
      // background: linear-gradient(-90deg, #fe7e4c, #ffa35f);
    }

    .submit-text-wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      p {
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
      }

      span {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}
</style>
