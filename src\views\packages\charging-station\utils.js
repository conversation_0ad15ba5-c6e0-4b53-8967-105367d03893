/**
 * 充电站相关工具函数
 */

import {
  STATION_STATUS_TEXT,
  BUSINESS_HOURS_TYPE_TEXT,
  CHARGING_METHODS_TEXT,
  OPERATION_TYPE_TEXT,
  PARKING_FEE_TYPE_TEXT,
  HIGHWAY_MODE_TEXT,
  STATION_TYPE_TEXT,
  PARKING_TYPES_TEXT,
  SERVICES_TEXT,
  BENEFITS_TEXT,
  TAG_TYPE_MAP,
  SERVICE_ICON_MAP,
  DISTANCE_THRESHOLDS,
  AVAILABILITY_STATUS,
  AVAILABILITY_THRESHOLDS,
} from './constants';

/**
 * 格式化距离显示
 * @param {number} distance - 距离（米）
 * @returns {string} 格式化后的距离字符串
 */
export function formatDistance(distance) {
  if (distance < DISTANCE_THRESHOLDS.METER_TO_KM) {
    return `${Math.round(distance)}m`;
  } else if (distance < DISTANCE_THRESHOLDS.PRECISION_THRESHOLD) {
    return `${(distance / 1000).toFixed(1)}km`;
  } else {
    return `${Math.round(distance / 1000)}km`;
  }
}

/**
 * 获取充电站状态文本
 * @param {'OPEN'|'CLOSED'|'DELETED'} status - 状态
 * @returns {string} 状态文本
 */
export function getStatusText(status) {
  return STATION_STATUS_TEXT[status] || '未知';
}

/**
 * 获取营业时间类型文本
 * @param {'TWENTY_FOUR_HOURS'|'OPEN'|'UNKNOWN'} type - 营业时间类型
 * @returns {string} 营业时间文本
 */
export function getBusinessHoursTypeText(type) {
  return BUSINESS_HOURS_TYPE_TEXT[type] || '未知';
}

/**
 * 获取充电方式文本
 * @param {string[]} methods - 充电方式数组
 * @returns {string[]} 充电方式文本数组
 */
export function getChargingMethodsText(methods) {
  if (!Array.isArray(methods)) return [];
  return methods.map(method => CHARGING_METHODS_TEXT[method] || method);
}

/**
 * 获取运营类型文本
 * @param {'SELF_OPERATED'|'NON_SELF_OPERATED'|'INTERCONNECTED'|'PRIVATE'|'COOPERATION'} type - 运营类型
 * @returns {string} 运营类型文本
 */
export function getOperationTypeText(type) {
  return OPERATION_TYPE_TEXT[type] || '未知';
}

/**
 * 获取停车费类型文本
 * @param {'TIME_LIMITED_FREE'|'PAID'|'FREE'} type - 停车费类型
 * @returns {string} 停车费类型文本
 */
export function getParkingFeeTypeText(type) {
  return PARKING_FEE_TYPE_TEXT[type] || '未知';
}

/**
 * 获取高速模式文本
 * @param {'ON_HIGHWAY'|'NEAR_HIGHWAY'|'NONE'} mode - 高速模式
 * @returns {string} 高速模式文本
 */
export function getHighwayModeText(mode) {
  return HIGHWAY_MODE_TEXT[mode] || '普通充电桩';
}

/**
 * 获取电站类型文本
 * @param {'PUBLIC'|'PRIVATE'} type - 电站类型
 * @returns {string} 电站类型文本
 */
export function getStationTypeText(type) {
  return STATION_TYPE_TEXT[type] || '未知';
}

/**
 * 获取停车场类型文本
 * @param {string[]} types - 停车场类型数组
 * @returns {string[]} 停车场类型文本数组
 */
export function getParkingTypesText(types) {
  if (!Array.isArray(types)) return [];
  return types.map(type => PARKING_TYPES_TEXT[type] || type);
}

/**
 * 获取服务设施文本
 * @param {string[]} services - 服务设施数组
 * @returns {string[]} 服务设施文本数组
 */
export function getServicesText(services) {
  if (!Array.isArray(services)) return [];
  return services.map(service => SERVICES_TEXT[service] || service);
}

/**
 * 获取权益文本
 * @param {string[]} benefits - 权益数组
 * @returns {string[]} 权益文本数组
 */
export function getBenefitsText(benefits) {
  if (!Array.isArray(benefits)) return [];
  return benefits.map(benefit => BENEFITS_TEXT[benefit] || benefit);
}

/**
 * 获取标签类型（用于Vant Tag组件）
 * @param {string} tag - 标签名称
 * @returns {string} 标签类型
 */
export function getTagType(tag) {
  return TAG_TYPE_MAP[tag] || 'default';
}

/**
 * 获取服务设施图标
 * @param {string} service - 服务名称
 * @returns {string} 图标名称
 */
export function getServiceIcon(service) {
  return SERVICE_ICON_MAP[service] || 'star-o';
}

/**
 * 计算两点间距离（简化版，实际项目中建议使用专业地理计算库）
 * @param {number} lat1 - 点1纬度
 * @param {number} lng1 - 点1经度
 * @param {number} lat2 - 点2纬度
 * @param {number} lng2 - 点2经度
 * @returns {number} 距离（米）
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLng = ((lng2 - lng1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}

/**
 * 生成导航URL
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @param {string} name - 地点名称
 * @returns {string} 导航URL
 */
export function generateNavigationUrl(lng, lat, name) {
  const encodedName = encodeURIComponent(name);
  return `https://uri.amap.com/navigation?to=${lng},${lat},${encodedName}&mode=car&policy=1&src=myapp&coordinate=gaode&callnative=0`;
}

/**
 * 验证地理坐标是否有效
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {boolean} 是否有效
 */
export function isValidCoordinate(lat, lng) {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  );
}

/**
 * 格式化电压范围显示
 * @param {number} minVoltage - 最小电压
 * @param {number} maxVoltage - 最大电压
 * @returns {string} 电压范围文本
 */
export function formatVoltageRange(minVoltage, maxVoltage) {
  if (!minVoltage && !maxVoltage) return '--';
  if (minVoltage && maxVoltage && minVoltage !== maxVoltage) {
    return `${minVoltage}-${maxVoltage}V`;
  }
  return `${maxVoltage || minVoltage}V`;
}

/**
 * 格式化充电桩数量显示
 * @param {number} available - 可用数量
 * @param {number} total - 总数量
 * @returns {string} 充电桩数量文本
 */
export function formatChargerCount(available, total) {
  if (typeof available !== 'number' || typeof total !== 'number') {
    return '--';
  }
  // return `${available}/${total}`;
  // 目前获取不到可用充电枪数量，暂时只显示总数
  return `${total}`;
}

/**
 * 获取充电桩类型信息
 * @param {Object} station - 充电站数据
 * @returns {Array} 充电桩类型数组
 */
export function getChargerTypes(station) {
  const types = [];

  // 直流充电桩
  if (station.dcCount > 0) {
    types.push({
      type: 'dc',
      name: '快',
      total: station.dcCount,
      available: 0, //
      maxPower: station.maxPower || 0,
    });
  }

  // 交流充电桩
  if (station.acCount > 0) {
    types.push({
      type: 'ac',
      name: '慢',
      total: station.acCount,
      available: 0, // 需要后端提供具体数据
      maxPower: station.maxPower || 0,
    });
  }

  return types;
}

/**
 * 获取充电站标签
 * @param {Object} station - 充电站数据
 * @returns {Array} 标签数组
 */
export function getStationTags(station) {
  const tags = [];

  // 营业时间标签
  if (station.businessHoursType === 'TWENTY_FOUR_HOURS') {
    tags.push({ text: '24小时', type: 'success' });
  }

  // 充电方式标签
  if (
    station.chargingMethods &&
    station.chargingMethods.includes('SUPER_FAST')
  ) {
    tags.push({ text: '超充', type: 'danger' });
  } else if (
    station.chargingMethods &&
    station.chargingMethods.includes('DC_FAST')
  ) {
    tags.push({ text: '快充', type: 'primary' });
  }

  // 停车费标签
  if (station.parkingFeeType === 'FREE') {
    tags.push({ text: '免费停车', type: 'success' });
  } else if (station.parkingFeeType === 'TIME_LIMITED_FREE') {
    tags.push({ text: '限时免费', type: 'warning' });
  }

  // 运营类型标签
  if (station.operationType === 'SELF_OPERATED') {
    tags.push({ text: '直营', type: 'primary' });
  }

  return tags;
}

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(price) {
  if (typeof price !== 'number') return '价格待定';
  return `${price.toFixed(1)}元/度`;
}

/**
 * 格式化充电桩数量显示
 * @param {number} available - 可用数量
 * @param {number} total - 总数量
 * @returns {string} 格式化后的数量字符串
 */
export function formatPileCount(available, total) {
  return `${available}/${total}个`;
}

/**
 * 检查充电站是否营业
 * @param {string} status - 充电站状态
 * @param {string} businessHours - 营业时间
 * @returns {boolean} 是否营业
 */
export function isStationOpen(status, businessHours) {
  if (status !== 'open') return false;
  if (businessHours === '24小时营业') return true;

  // 这里可以添加更复杂的营业时间判断逻辑
  const now = new Date();
  const currentHour = now.getHours();

  // 简单判断：6点到22点为营业时间
  return currentHour >= 6 && currentHour < 22;
}

/**
 * 获取充电站可用性状态
 * @param {Object} station - 充电站对象
 * @returns {Object} 可用性状态
 */
export function getStationAvailability(station) {
  const totalAvailable =
    (station.fastCharging?.available || 0) +
    (station.slowCharging?.available || 0);
  const totalPiles =
    (station.fastCharging?.total || 0) + (station.slowCharging?.total || 0);

  let status = AVAILABILITY_STATUS.UNAVAILABLE;
  let text = '暂无可用';
  let color = '#ff4d4f';

  if (totalAvailable > 0) {
    const ratio = totalAvailable / totalPiles;
    if (ratio >= AVAILABILITY_THRESHOLDS.SUFFICIENT) {
      status = AVAILABILITY_STATUS.AVAILABLE;
      text = '充足';
      color = '#52c41a';
    } else if (ratio >= AVAILABILITY_THRESHOLDS.LIMITED) {
      status = AVAILABILITY_STATUS.LIMITED;
      text = '紧张';
      color = '#faad14';
    } else {
      status = AVAILABILITY_STATUS.FEW;
      text = '稀少';
      color = '#ff7a45';
    }
  }

  return { status, text, color, available: totalAvailable, total: totalPiles };
}

/**
 * 本地存储工具
 */
export const storage = {
  /**
   * 设置本地存储
   * @param {string} key - 键名
   * @param {any} value - 值
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('设置本地存储失败:', error);
    }
  },

  /**
   * 获取本地存储
   * @param {string} key - 键名
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值
   */
  get(key, defaultValue = null) {
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : defaultValue;
    } catch (error) {
      console.error('获取本地存储失败:', error);
      return defaultValue;
    }
  },

  /**
   * 删除本地存储
   * @param {string} key - 键名
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('删除本地存储失败:', error);
    }
  },
};
