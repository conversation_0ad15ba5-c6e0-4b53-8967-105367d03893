/* eslint-disable vue/no-use-v-if-with-v-for */
<template>
  <content-view :status="status" :refreshAction="refreshAction" @refresh="refresh"  @reload="reload" @scroll-bottom="loadMore">
    <template v-if="status == AppStatus.READY">
      <transition-group name="order-list" tag="div" class="orders">
        <template v-for="item in orders">
          <div
            v-if="item"
            :key="item.sysOid"
            class="order"
            @click="goDetail(item)">
            <biz-image
              :src="getGoodsLogo(item.orderType)"
              class="order-goods-logo"
              :lazy="true"
            >
            </biz-image>
            <div class="order-content">
              <div class="order-head flex-row">
                <div class="flex-item order-title">
                  <div class="order-name">{{item.title}}</div>
                </div>
                <span class="order-status">{{formatOrderStatus(item)}}</span>
              </div>
              <div class="flex-row order-body">
                <ul class="flex-item flex-col order-attrs">
                  <li>
                    金额：<span class="rmb">{{item.amount}}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </template>
      </transition-group>
      <list-loader v-if="orders.length > 5" :options="$_loader_options" @load="loadMore"></list-loader>
      <list-placeholder v-if="!orders.length" icon="~@/assets/images/mall/empty.png">没有记录</list-placeholder>
    </template>
    </content-view>
</template>

<script>
import { formatDate } from '@/utils';
import { mixinLoader, mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { CarOrdersStatus, InspectionOrdersStatus, CardBuyOrdersStatus, ActionOrdersStatus, RechargeOrdersStatus, GroupBuyOrdersStatus, FoodsOrdersStatus, MemberNewOrdersStatus } from './enums';
import { dialog, toast, loading } from '@/bus';
import { getAllOrderList } from '@/api';

function isSameModule(parent, child) {
  const parentBase = parent.split('/')[1];
  const childBase = child.split('/')[1];
  return parentBase === childBase;
}
const PAGE_SIZE = 10;
export default {
  name: 'ALLOrderList',
  // props: {
  //   category: {
  //     type: String,
  //     default: '',
  //   }
  // },
  mixins: [mixinLoader, mixinAuthRouter],
  components: {

  },
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      orders: [],
      refreshAction: 1,
    };
  },
  // TODO: 改成使用mixin_loader的形式
  computed: {
    filterParams() {
      return {
        // payStatus: 1, // 只查询已付款的订单
        ...this.$_loader_params,
      }
    },
  },
  methods: {
    // 格式化状态
    formatOrderStatus(item) {
      if (item.orderType == 'car') { // 洗车养车
        return this.displayOrderStatus(CarOrdersStatus, item.status)
      } else if (item.orderType == 'inspection') { // 审车、车务
        return this.displayOrderStatus(InspectionOrdersStatus, item.status)
      } else if (item.orderType == 'card-buy') { // 洗车卡、保养卡
        return this.displayOrderStatus(CardBuyOrdersStatus, item.status)
      } else if (item.orderType == 'action') { // 活动
        return this.displayOrderStatus(ActionOrdersStatus, item.status)
      } else if (item.orderType == 'recharge' || item.orderType == 'direct-recharge') { // 话费充值
        return this.displayOrderStatus(RechargeOrdersStatus, item.status)
      } else if (item.orderType == 'group-buy') { // 团购
        return this.displayOrderStatus(GroupBuyOrdersStatus, item.status)
      } else if (item.orderType == 'foods') { // 美食
        return this.displayOrderStatus(FoodsOrdersStatus, item.status)
      } else if (item.orderType == 'memberNew') { // 购买会员
        return this.displayOrderStatus(MemberNewOrdersStatus, item.status)
      }
    },
    // 商城订单
    displayOrderStatus(e, stat) {
      const status = e.getEnum(stat);
      if (status === null) return `异常${stat}`;
      return status.desc;
    },
    goDetail(item) {
      if (item.orderType == 'car') { // 洗车养车
        this.$_router_push('/order/' + item.orderId);
      } else if (item.orderType == 'inspection') { // 审车、车务
        this.$_router_push('/vehicle-business/order/' + item.orderId);
      } else if (item.orderType == 'card-buy') { // 洗车卡、保养卡
        this.$_router_push('/washcard/mycard/' + item.orderId);
      } else if (item.orderType == 'action') { // 活动
        this.$_router_pageTo(item.url, {
          titleBar: true,
          pulldownRefresh: false,
          shareButton: true,
        })
      } else if (item.orderType == 'recharge') { // 话费充值
        // 规避 通过landing基座加载的模块，发生跨模块跳转的副作用
        const nextPath = `/mobile-recharge/order/${item.orderId}`;
        const parentPath = this.$route.path;
        if (isSameModule(parentPath, nextPath)) {
          this.$_auth_push(nextPath)
        } else {
          this.$_router_pageTo(nextPath, {
            theme: 'light',
          });
        }
      } else if (item.orderType == 'direct-recharge') { // 话费直冲
        this.$_auth_push(`/mobile-direct-recharge/order/${item.orderId}`);
      } else if (item.orderType == 'group-buy') { // 团购
        this.$_router_push('/mall/order/' + item.orderId);
      } else if (item.orderType == 'foods') { // 美食
        this.$_auth_push('/mallg2/order/' + item.orderId);
      } else if (item.orderType == 'memberNew') { // 购买会员
        // this.$_router_push(url);
      }
      // this.$_router_push(url);
    },
    getGoodsLogo(type) {
      if (type == 'car') { // 洗车养车
        return require('./images/car.png');
      } else if (type == 'inspection') { // 审车、车务
        return require('./images/inspection.png');
      } else if (type == 'card-buy') { // 洗车卡、保养卡
        return require('./images/card-buy.png');
      } else if (type == 'action') { // 活动
        return require('./images/action.png');
      } else if (type == 'recharge' || type == 'direct-recharge') { // 话费充值
        return require('./images/recharge.png');
      } else if (type == 'group-buy') { // 团购
        return require('./images/group-buy.png');
      } else if (type == 'foods') { // 美食
        return require('./images/foods.png');
      } else if (type == 'memberNew') { // 购买会员
        return require('./images/vip.png');
      }
      // this.$_router_push(url);
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.refreshQuietly();
    },
    // 静默刷新列表数据
    refreshQuietly() {
      const currentPage = this.$_loader_getPage();
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(this.orders.length);
      return this.getList().then(res => {
        this.$_loader_setPage(currentPage);
        this.$_loader_setPageSize(PAGE_SIZE);
        return res;
      }, err => {
        // this.status = AppStatus.ERROR;
        toast().tip('刷新失败，请重试！');
        console.error(err);
      });
    },
    // 初始化函数
    init() {
      return this.initList();
    },
    // 失败重试
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    // 重新获取列表
    initList() {
      this.$_loader_setPage(1);
      this.$_loader_setPageSize(PAGE_SIZE);
      return this.getList().then(res => {
        this.status = AppStatus.READY;
        return res;
      }).catch(err => {
        this.status = AppStatus.ERROR;
        console.error(err);
        toast().tip(err);
        return Promise.reject(err);
      });
    },
    // 滚动加载更多
    loadMore() {
      if (!this.$_loader_couldLoadMore) return;
      const nextPage = this.filterParams.page + 1;
      this.getList(nextPage).catch(e => {
        toast().tip(e);
        console.error(e);
      })
    },
    // 下拉刷新
    refresh() {
      this.initList().then(res => {
        this.refreshAction = Date.now();
      }).catch(e => {
        toast().tip('刷新失败');
        this.refreshAction = Date.now();
      });
    },
    // 查询列表
    getList(page = 1) {
      const params = { ...this.filterParams, page };
      return this.$_loader_bind(getAllOrderList, res => {
        if (page === 1) {
          this.orders = res.list;
        } else {
          this.orders = this.orders.concat(res.list);
        }
        return res.list;
      }).load(params);
    },
  }
};
</script>
<style lang="scss" scoped>
  @import '~styles/mixin/index.scss';

  $border-color: #ececec;
  .tabs-orders{
    background: white;
    box-shadow: 0 1px 5px 1px #e7eaf5;
    z-index: 1;
  }
  .orders{
    position:relative;
  }
  .order-goods-logo{
    width:50px;
    height:50px;
    border-radius: 2px; /* px */
    margin: 3px 3px 0 0;
  }
  .order{
    background:white;
    padding:13px 0 6px 10px;
    margin:10px 0;
    display: flex;
    &.order-end .order-status{
      color:#AFAFAF;
    }
  }
  .icon-arrow-right {
    color: #6f6f6f;
    font-size: 14px;
    transform: scale(0.8);
  }
  .order-content {
    flex: 1;
  }
  .order-attrs{
    list-style:none;
    margin-top: 2px;
    line-height: 1.7;
  }
  .order-foot {
    @include border-top($border-color, 'before');
    // border-top:1px solid $border-color;
    padding-top: 5px;
    margin-left: 5px;
    margin-top: 4px;
    padding-right: 10px;
  }
  .order-title {
    // color: gray;
    font-weight: 700;
    margin-left: 5px;
    font-size: 16px;
  }
  .order-name {
    display: inline-block;
    max-width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: top;
  }
  .order-head {
    padding-right: 10px;
    // margin-top: 5px;
  }
  .order-end{
    .order-head::before{
     filter: grayscale(1);
    }
  }
  .order-attr__name {
    margin-right: 5px;
  }
  .order-body {
    padding: 0 5px;
    align-items: center;
    font-size: 14px;
    color: #8a8a8a;
    color: #696969;
  }
  .order-body-right {
    align-self: flex-end;
    line-height: 1;
  }
  .order-status{
    color:#F2903F;
    color:#2199f9;
    font-size:.8em;
  }
  .weui-btn+.weui-btn{
    margin-top:auto;
  }
  .weui-btn{
    border-radius: 3px;
    border-radius: 3px;
    background: white;
    box-shadow: none;
    border: 1px solid #cecece;
    // @include border-1px();
    padding: 0 .6em;
    line-height: 1.9;
    margin-left: 5px;
    &::after{
      display:none;
    }
  }
  .order-btn {
    color: #EE6E00;
    border-color: #f59645;
    background: white;
    line-height: 1.8;
    &:not(.weui-btn_disabled):active{
      background-color:#f59645;
      color:white;
    }
    &.order-btn__primary {
      background: #f59645;
      color: white;
    }
  }

  .order-tip{
    // margin-left:5px;
    color:#9D9D9D;
    font-size:0.9em;
    margin-top: 2px;
    &.order-status-highlight {
      color: #f59645;
    }
  }

  /*.order {
    transition: transform 300ms;
  }*/

 .scroller{
    overflow-y:hidden;
  }
  .vscroller{
    height:600PX;
    overflow-y:scroll;
  }
  .order {
    box-sizing: border-box;
  }
  .order-filters {
    margin-top: 10px;
    .tag-list {
      list-style: none;
      font-size: 14px;
    }
    .tag-item {
      display: inline-block;
      margin-left: 10px;
      border: 1px solid #b8b8b8;
      padding: 0 5px;
      border-radius: 5px;
      color: #272727;
      &.active {
        background: #49A8FC;
        color: white;
        border-color: #49a8fc;
      }
    }
  }
</style>
