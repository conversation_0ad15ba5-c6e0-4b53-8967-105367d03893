import APIModel from '@/api/APIModel';

const api = new APIModel({
  banners: '/Radio/base/infos?platform=WEIXIN',
  'event/list': '/Radio/actions/list?type=2&orient=1&max=15&sort=0.0',
  'event/orders': '/Radio/action/orders',
  buttons: '/Radio/base/infos?platform=WEIXIN',
  'bind/wxpub': '/action/business/api/business/account/bind/wxpub',
  'secretary/business': '/inspection/app/business/info/by/secretary/phone', // 根据小秘书手机号查询商家信息
  'bind/wxpub/secretary':
    '/inspection/app/business/info/wx/openid/set/by/secretary/phone', // 根据小秘书手机号绑定微信公众号
});

/**
 * 获取首页banners
 */
export function getBannerList() {
  return api.doGet('banners');
}

export function getHomeButtonList() {
  return api.doGet('buttons');
}

/**
 * 获取活动列表
 */
export function getEventList(query) {
  const params = Object.assign(
    {
      type: 2,
      orient: 1,
    },
    query
  );
  return api.doGet('event/list', params);
}

export function getEventOrderList(query) {
  const params = Object.assign(
    {
      t: Date.now(),
      orient: 0,
      max: 10,
    },
    query
  );
  return api.doGet('event/orders', params);
}

/**
 * 绑定微信公众号
 * @param {Object} params - 绑定参数
 * @param {string} params.openId - 微信openId
 * @param {string} params.phone - 手机号
 * @param {string} params.code - 验证码
 * @param {Array<number>} params.businessIds - 商家ID数组（支持多选）
 */
export function bindWxPubMallBusiness(params) {
  return api.doPost('bind/wxpub', params);
}

/**
 * 根据小秘书手机号查询商家信息
 * @param {Object} params
 * @param {string} params.phone 小秘书手机号
 */
export function getBusinessInfoBySecretaryPhone(params) {
  return api.doPost('secretary/business', params);
}

/**
 * 绑定微信公众号与商家的关系（根据小秘书手机号）
 * @param {Object} params
 * @param {string} params.openid 微信openid
 * @param {string} params.phone 小秘书手机号
 * @param {string} params.code 验证码
 * @param {Array} params.bids 商户ids
 */
export function bindWxPubBySecretaryPhone(params) {
  return api.postJSON('bind/wxpub/secretary', params);
}
