<template>
  <container
    class="page-mall theme-white"
    :class="{
      'home-mode': mode === PageMode.Home,
      'top-transparent': shouldTransparent,
    }"
    :style="pageStyles"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    header
  >
    <x-header :title="pageTitle" visible ref="header">
      <x-button
        v-if="mode !== PageMode.Home"
        slot="left"
        type="back"
      ></x-button>
      <div class="search-bar" @click="goToSearch">
        <svg class="icon-search">
          <use xlink:href="#icon-search"></use>
        </svg>
        <span class="search-word">{{ searchSuggest || '搜索商品' }}</span>
      </div>
      <!-- <x-button slot="right" type="order" @click="$_router_pageTo('/account/orders', { requireSignIn: true })"></x-button> -->
      <div
        class="header-btn"
        slot="right"
        @click="$_router_pageTo('/mall/shopping/cart', { requireSignIn: true })"
      >
        <svg class="icon-gouwuche">
          <use xlink:href="#icon-gouwuche"></use>
        </svg>
        <span v-if="shoppingCartNums" class="cart-nums">{{
          shoppingCartNums
        }}</span>
        <!-- <span class="cart-nums">5</span> -->
      </div>
    </x-header>
    <page-view class="mall-swiper-view" :status="status" @reload="reload">
      <template slot="head">
        <tabs2
          v-if="status == AppStatus.READY"
          ref="tabs"
          class="tab-goods"
          :tabs="categories"
          :index="currentCategoryIndex"
          @change="onTabChange"
          :scrollable="true"
          :textColor="tabTextColor"
        >
        </tabs2>
      </template>
      <template v-if="status == AppStatus.READY">
        <goods-list-swiper
          v-if="page.config.id"
          ref="swiper"
          :initialIndex="currentCategoryIndex"
          :categories="categories"
          :pageConfig="page.config"
          @slide="onSlide"
          @refresh="refreshPageConfig"
          :platform="platform"
          :topHeight="topHeight"
        />
        <goods-list-swiper-old
          v-else
          ref="swiper"
          :initialIndex="currentCategoryIndex"
          :categories="categories"
          :pageConfig="page.config"
          @slide="onSlide"
          @refresh="refreshPageConfig"
          :platform="platform"
          :topHeight="topHeight"
        />
      </template>
    </page-view>
  </container>
</template>

<script>
import {
  formatDate,
  parseAppointmentTime,
  getAbsolutePath,
  getAppURL,
} from '@/utils';
import { mixinAuthRouter, mixinShare, mixinLoader } from '@/mixins';
import { AppStatus, MallGoodsType } from '@/enums';
import { getImageURL } from '@/common/image';
import { setRecommender } from '@/store/storage';
import { serialize } from '@/api/request';
import { login, setTitle, pushWebView } from '@/bridge';
import { dialog, toast, loading } from '@/bus';
import {
  getGoodsList,
  getGoodsCategories,
  getShoppingCartNums,
  getMallIndexConfig,
  getMallIndexDefaultConfig,
} from '@pkg/mall/api';

import {
  isAndroid,
  isInWeixin,
  isInJglh,
  isInWeApp,
  isInUnionPayMP,
  isInAliApp,
} from '@/common/env';
import GoodsListSwiperOld from './_GoodsListSwiper.vue';
import GoodsListSwiper from './_GoodsListSwiperV2.vue';

import Tabs2 from '@/components/Tabs2.vue';

/**
 * ISSUE: 使用swiper做多页面滑动切换时的问题
 * iOS端，滚动到底部时，继续上拉会抖动
 * 左右滑动卡顿，不流畅
 */

const PAGE_SIZE = 10;
const PageMode = {
  Home: 1,
  SubPage: 2,
};

export default {
  name: 'Mall',
  components: {
    GoodsListSwiperOld,
    GoodsListSwiper,
    Tabs2,
  },
  props: {
    headerMode: {
      type: String,
      default() {
        return 'home';
      },
    },
  },
  mixins: [mixinAuthRouter, mixinLoader, mixinShare],
  data() {
    // 页面模式：主要区别是首页无返回按钮，作为二级页面时会有一个返回按钮
    let pageMode;
    if (isInUnionPayMP) {
      pageMode = this.headerMode == 'home' ? PageMode.Home : PageMode.SubPage;
    } else {
      // pageMode = this.headerMode == 'home' ? PageMode.Home : PageMode.SubPage;
      pageMode =
        location.hash.indexOf('home') > -1 ? PageMode.Home : PageMode.SubPage;
    }
    // 热门类目：热门类目会被加上一个hot标识
    const hotCategory = this.$route.query.hc;

    // 平台类型：主要用于规避小程序类目审核，小程序内过滤部分品类商品
    const platform = this.$route.query.platform || '';

    // 搜索建议：用于设置搜索输入框占位搜索提示语
    const searchSuggest = this.$route.query.suggest || '';

    return {
      AppStatus,
      PageMode,
      status: AppStatus.LOADING,
      platform: platform,
      hotCategory: hotCategory, // 热门类目
      searchSuggest,
      currentCategoryIndex: 0,
      progress: 0,
      page: {
        categories: [],
        banners: [],
        config: {},
      },
      mode: pageMode,
      shoppingCartNums: 0,
      topHeight: 0, // 顶部搜索框+tabs高度
    };
  },
  computed: {
    pageTitle() {
      if (isInJglh) return '';
      return '领航商城';
    },
    currentCategory() {
      return this.categories[this.currentCategoryIndex];
    },
    categories() {
      const categories = this.page.categories
        .filter(item => {
          return item.name;
        })
        .filter(item => {
          // if (isInWeApp) {
          //   return item.name.indexOf('酒') == -1;
          // }
          if (isInAliApp) {
            return !item.name.includes('珠宝') && !item.name.includes('活动');
          }
          return true;
        })
        .map(item => {
          return {
            ...item,
            name: item.name,
            value: item.id,
            hot: item.id == this.hotCategory,
          };
        });
      return [
        {
          name: '首页',
          value: -1,
        },
        {
          name: '全部',
          value: '',
        },
        // 2023-05-05, 运营方案修改，不展示会员专区
        // {
        //   name: '会员专区',
        //   value: -2,
        // },
        ...categories,
      ];
    },
    pageStyles() {
      let config = this.page.config;
      let styles = {};
      // 根据条件设置背景图及背景颜色
      if (
        this.currentCategoryIndex == 0 &&
        config &&
        config.backgroundColor &&
        !config.backgroundImage
      ) {
        styles.background = `${config.backgroundColor}`;
      }
      if (
        this.currentCategoryIndex == 0 &&
        config &&
        !config.backgroundColor &&
        config.backgroundImage
      ) {
        styles.background = `url(${getImageURL(
          config.backgroundImage
        )}) no-repeat left top`;
        styles.backgroundSize = '100% auto';
      }
      if (
        this.currentCategoryIndex == 0 &&
        config &&
        config.backgroundColor &&
        config.backgroundImage
      ) {
        styles.background = `${config.backgroundColor} url(${getImageURL(
          config.backgroundImage
        )}) no-repeat left top`;
        styles.backgroundSize = '100% auto';
      }
      return styles;
    },
    tabTextColor() {
      let config = this.page.config;
      // 根据条件设置背景图及背景颜色
      if (this.currentCategoryIndex == 0 && config && config.fontColor) {
        return config.fontColor;
      }
      return '';
    },
    shouldTransparent() {
      let config = this.page.config;
      // 根据条件设置背景图及背景颜色
      if (
        this.currentCategoryIndex == 0 &&
        config &&
        (config.backgroundColor || config.backgroundImage)
      ) {
        return true;
      }
      return false;
    },
  },
  watch: {
    status: {
      handler(val, oldVal) {
        // 当page.config发生变化时执行的逻辑
        let config = this.page.config;
        // 根据条件设置背景图及背景颜色
        if (val == AppStatus.READY && config && config.backgroundImage) {
          setTimeout(() => {
            let headerOffsetHeight = 0;
            let tabsOffsetHeight = 0;
            let headerRef = this.$refs.header;
            let tabsRef = this.$refs.tabs;
            if (headerRef) {
              headerOffsetHeight = headerRef.$el.offsetHeight;
            }
            if (tabsRef) {
              tabsOffsetHeight = tabsRef.$el.offsetHeight;
            }
            this.topHeight = headerOffsetHeight + tabsOffsetHeight;
          }, 100);
        }
      },
    },
  },
  methods: {
    goToSearch() {
      this.$_router_pageTo(`/mall/search?sw=${this.searchSuggest}`, {
        theme: 'light',
      });
    },
    onTabChange(index) {
      this.$refs.swiper.slideTo(index);
    },
    onLeave() {
      console.log('leave');
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.getCartNums();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    init() {
      getGoodsCategories().then(res => {
        this.page.categories = res;
        this.handleParams();
        this.getCartNums();
        this.initShareInfo();
        this.getPageConfig().finally(() => {
          setTimeout(() => {
            this.status = AppStatus.READY;
          }, 10);
        });
      });
    },
    // 刷新页面配置
    refreshPageConfig(flag) {
      this.getPageConfig()
        .then(() => {
          flag && flag();
        })
        .catch(() => {
          flag && flag();
          toast().tip('刷新失败');
        });
    },
    initShareInfo() {
      const refUser = this.$route.query.ref;
      if (refUser && this.$_auth_userInfo.uid !== refUser) {
        setRecommender(refUser);
      }
      // const path = this.$route.path;
      // const pageParam = this.hideHead ? 'hide-head' : '';
      const title = '河南交通广播FM104.1 交广领航商城';
      const desc = '河南交通广播FM104.1 交广领航商城';
      // const image = getImageURL(this.goodsCover, '_cover.jpg');
      let path = this.$route.fullPath;
      // 已登录状态分享携带推荐信息
      if (this.$_auth_isLoggedIn) {
        const querys = serialize({
          ...this.$route.query,
          ref: this.$_auth_userInfo.uid,
        });
        path = `${this.$route.path}?${querys}`;
      }
      let shareInfo = {
        link: getAppURL(path),
        title: title,
        desc: desc,
        // imgUrl: image,
      };
      if (isInWeApp) {
        shareInfo.imgUrl = '';
      }
      // console.log(shareInfo);
      this.setShareInfo(shareInfo);
    },
    // 处理初始化参数（依赖于接口获取到的数据，所以需要接口返回数据后再执行
    handleParams() {
      // 默认类别
      const category = this.$route.query.category;
      // 2023-05-05, 运营方案修改，不展示会员专区
      // if (category == '1041') {
      //   this.currentCategoryIndex = 2;
      //   return
      // }
      const index = this.categories
        .map(item => String(item.id))
        .indexOf(category);
      if (index > -1) {
        this.currentCategoryIndex = index;
      }
    },
    // 获取购物车商品数量
    getCartNums() {
      if (!this.$_auth_isLoggedIn) return;
      return getShoppingCartNums()
        .then(res => {
          this.shoppingCartNums = res;
        })
        .catch(err => {
          toast().tip(err);
        });
    },

    // 获取页面配置详情
    getPageConfig() {
      const pageId = this.$route.query.pageId;
      if (pageId) {
        return getMallIndexConfig(pageId)
          .then(res => {
            this.page.config = res || {};
          })
          .catch(() => {
            // toast().tip(err);
          });
      } else {
        return getMallIndexDefaultConfig()
          .then(res => {
            this.page.config = res || {};
          })
          .catch(() => {
            // toast().tip(err);
          });
      }
    },

    onSlide(index) {
      // console.log(index);
      this.currentCategoryIndex = index;
    },
    onProgress(progress) {
      // this.progress = progress;
      // console.log(progress)
    },
  },
};
</script>
<style lang="scss">
/* .container.theme-white {
    background: white;
  } */
</style>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

$item-border-color: #dadada;
.item-list {
  display: flex;
  flex-wrap: wrap;
  padding: 5px;
  .goods-item {
    width: 49%;
    width: calc(50% - 10px);
    margin: 5px;
    background: white;
    border-radius: 5px;
    padding-bottom: 10px;
    overflow: hidden;
  }
}
.page-nav {
  background: white;
  // border-bottom: 1px solid #efefef;
  @include border-bottom($item-border-color, 'after');
  .nav-spliter {
    background: #dad7d7;
    transform: scale(0.5);
    width: 1px; /* px */
  }
  .nav-item {
    flex: 1;
    text-align: center;
    padding: 6px;
    &::before {
      font-family: iconfont;
      margin-right: 2px;
      font-size: 1.1em;
      color: gray;
    }
    &.nav-item-address::before {
      content: '\e647';
    }
    &.nav-item-order::before {
      content: '\e622';
    }
  }
}

.page-mall {
  > .header {
    // padding-left: 45px;
    // border-bottom: 1px solid white;
  }
  ::v-deep .header-button {
    position: relative;
  }
  // ios下，content-view嵌套时，swiper布局中下拉刷新距离抖动，临时禁用此属性
  ::v-deep .mall-swiper-view > .scroller {
    -webkit-overflow-scrolling: auto;
  }
  .tab-goods {
    // width: calc(100% - 45px);
    // margin-left: 45px;
    // width: calc(100vw - 45px);
    // margin-left: 45px;
    // flex: 1;
    // border-bottom: 1px solid #f7f7f7;
    box-shadow: 0 1px 10px 1px rgba(0, 0, 0, 0.1);
  }
  &.home-mode > .header {
    padding-left: 0;
  }
  // &.home-mode .tab-goods {
  //   margin-left: 0;
  //   width: 100%;
  // }
}
.tab-goods ::v-deep {
  background: white;
  line-height: 1.7;
  color: rgb(158, 158, 163);
  font-size: 14px;
  font-weight: 500;
  transition: background-color 300ms;
  .tab-items {
    border-bottom: 0;
  }
  .tab-item {
    margin: 0 5px;
    padding-top: 10px;
    padding-bottom: 10px;
    transition: background-color 300ms;
  }
  .tab-item.tab-item-hot::before {
    content: 'hot';
    position: absolute;
    top: 1px;
    right: 0;
    font-size: 12px;
    color: white;
    background: red;
    line-height: 1;
    border-radius: 5px;
    padding: 1px 3px;
    transform: scale(0.9);
  }
  .tab-item-active {
    color: #ffffff;
    transform: scale(1);
    font-weight: 400;
    &::after {
      display: none;
    }
    span {
      background: $lh-2022-primary-color;
      display: inline-block;
      padding: 0 4px;
      line-height: 24px;
      border-radius: 2px;
    }
  }
}
.search-bar {
  line-height: 1;
  background: #eee;
  padding: 8px 5px;
  // width: 300px;
  border-radius: 30px;
  font-size: 14px;
  position: relative;
  color: #b1b1b1;
  text-align: left;
  flex: 1;
  margin: 0 5px 0 15px;
  &.search-bar-word {
    text-align: left;
    color: gray;
    .search-word {
      color: #3c3c3c;
    }
  }
  .icon-search {
    font-size: 1.1em;
    margin-right: 2px;
    margin-left: 5px;
  }
  /*&::before{
      content: "\e600";
      font-family:iconfont;
      margin-right:2px;
    }*/
}
.icon-gouwuche {
  font-size: 24px;
}
.header {
  transition: background-color 300ms;
}
.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  .cart-nums {
    position: absolute;
    left: 22px;
    top: -2px;
    min-width: 30px;
    height: 30px;
    padding: 0 5px;
    box-sizing: border-box;
    background: $lh-2022-primary-color;
    text-align: center;
    line-height: 30px;
    border-radius: 15px;
    font-size: 18px;
    color: #ffffff;
    transform: scale(0.5);
    transform-origin: left center;
  }
}
.top-transparent ::v-deep {
  .header {
    background: transparent !important;
    border-bottom: none;
    .icon-gouwuche {
      color: white !important;
    }
    .cart-nums {
      background: white !important;
      color: $lh-2022-primary-color !important;
    }
  }
  .tab-goods,
  .goods-banner,
  .mall-banner {
    background: transparent !important;
    box-shadow: none !important;
  }
  .tab-item-active {
    transform: scale(1.2);
    font-weight: 700;
    background: transparent;
    span {
      background: transparent;
    }
  }
  .goods-category {
    background: #f1f1f1;
  }
  .pulldown-content {
    color: #dddddd;
  }
}
</style>
