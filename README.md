# 介绍

  本项目目前为在C端运行的单页应用

  生产运行环境为 `交广领航WebView`， `微信WebView`，`交广领航微信小程序`

  部分功能依赖于交广领航JS-SDK和微信JS-SDK

# 必读

[Tips](./doc/Tips.md)

[TODO](./doc/TODO.md)

[技改](./doc/技改.md)

## 部署

通过jekins控制台部署正式、测试，以测试为例

1.进入工程“jglh-weapp-test”

2.左侧菜单“Build with Parameters”，选择要构建的分支，正式、测试一般都为“origin/dev”分支

3.点击开始构建，等待构建完成即可访问

## 部署回滚

通过jekins控制台回滚，以正式为例

1.进入工程“jglh-weapp-online”

2.左侧菜单“Build with Parameters”，选择status-Rollback，填写要回滚的版本号（左侧构建历史的id，如#481，则填入481）

3.点击开始构建，即可回滚完成

## 安装和启动

```bash

# 部分功能依赖于公司内部私有NPM包，需要将镜像源设置为私库地址
npm config set registry=http://registry.jgrm.net

npm login ...

# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run serve

# build for production with minification
npm run build
```

[开发指引](./doc/DEV.md)
