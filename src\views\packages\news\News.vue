<template>
  <page
    class="container-news"
    :class="{ 'no-header': hideHeader }"
    @ready="test"
    @resume="onResume"
  >
    <x-header :title="pageTitle" syncTitle>
      <x-button v-if="!hideHeader" slot="left" type="back"></x-button>
    </x-header>
    <div class="news-page-content">
      <van-search
        v-if="mode == PageMode.Search"
        ref="inputRef"
        v-model="sw"
        show-action
        :placeholder="searchPlaceholder"
        @search="doSearch"
        @focus="inputFocus"
        @input="inputChange"
      >
        <template #action>
          <div @click="doSearch">搜索</div>
        </template>
      </van-search>
      <news-nav ref="nav" @change="onNewsNavChange"></news-nav>
      <!-- <div>搜索区</div> -->
      <news
        @slide="onNewsSlide"
        ref="news"
        :searchTime="searchTime"
        :sw="sw"
        :searchIndex="searchIndex"
      ></news>
    </div>
  </page>
</template>

<script>
import { formatDate, getAbsolutePath, getAppURL } from '@/utils';
import { getImageURL } from '@/common/image';
import { toast, dialog, loading } from '@/bus';
import { getNewsCategories } from '@/api';
import { AppStatus, NativeView } from '@/enums';
import { isInJglh, isInWeixin, isInWeApp, isIOS } from '@/common/env';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import NewsNav from './_NewsNav.vue';
import News from './_News.vue';
import {
  pushWebView,
  popWebView,
  pushNativeView,
  login,
  setTitle,
} from '@/bridge';
import { Icon, Button, Search } from 'vant';

const PageMode = {
  Zone: 'zone',
  Search: 'search',
};

const PAGE_SIZE = 10;
export default {
  name: 'news-list',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    News,
    NewsNav,
    [Search.name]: Search,
  },
  data() {
    // 页面平台区分 默认交广新闻，emergency-应急平台
    const source = this.$route.query.source || '';
    // 页面模式，搜索/专区
    const mode = this.$route.query.mode || PageMode.Zone;
    // 默认搜索关键字
    const searchFallback = this.$route.query.sw;
    return {
      AppStatus,
      status: AppStatus.LOADING,
      currentNav: 0,
      home: {
        news: [],
        types: [],
      },
      newsList: {},
      offsetTime: Date.now(),
      slider: 0,
      newsType: 0,
      refreshAction: 1,
      newsTabScrollable: false,
      readList: {},
      pageTitle: '应急知识',
      PageMode,
      mode, // 页面模式，搜索/专区
      sw: '', // 搜索框值
      searchFallback: searchFallback, // 链接指定搜索关键字
      searchTime: 0, // 搜索按钮点击时间
      searchIndex: -1, // 搜索板块的索引
      source,
    };
  },
  computed: {
    newsTabs() {
      return this.home.types.map(item => {
        return {
          id: item.id,
          name: item.title,
          value: item.id,
        };
      });
    },
    searchPlaceholder() {
      return this.searchFallback || '输入关键词';
    },
    hideHeader() {
      // 是否隐藏顶部标题+返回按钮，目前只有source:mayTravel需要显示
      return this.source !== 'mayTravel';
    },
  },
  mounted() {
    // performance.mark('mounted')
    // console.log('mounted...')
    this.init();
    const pageTitle = this.$route.query.title
      ? this.$route.query.title
      : '应急知识';
    // setTitle(pageTitle);
    this.pageTitle = pageTitle;
    if (this.source === 'emergency') {
      this.initShareInfo();
    }
  },
  methods: {
    test() {
      console.log('init...');
    },
    init() {
      return this.getNewsCategory();
    },
    getNewsCategory() {
      return getNewsCategories()
        .then(res => {
          this.home.types = res;
          // saveNewsCategory(res);
          if (res.length) {
            this.$nextTick(() => {
              this.newsTabScrollable = res.length > 4;
            });
          }
          this.status = AppStatus.READY;
        })
        .catch(e => {
          console.error(e);
          this.status = AppStatus.ERROR;
        });
    },
    initShareInfo() {
      // 设置邀请链接分享信息
      // const wechatPath = `/activity/topic/${this.topicId}`;
      // const jglhWechatURL = getAppURL(wechatPath, {
      //   search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share2' : '?jglh',
      // });

      const shareInfo = {
        link: location.href,
        title: `河南交通广播${this.pageTitle}`,
        desc: `河南交通广播河南省应急广播${this.pageTitle}`,
        // imgUrl: getImageURL(this.infoData.shareImage),
      };
      this.$_share_update(shareInfo);
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onNewsSlide(index) {
      console.log('onNewsChange:', index);
      this.newsType = index;
      this.$refs.nav && this.$refs.nav.setActiveIndex(index);
      this.sw = '';
    },
    onNewsNavChange(arg) {
      console.log('switchType...', arg);
      this.newsType = arg;
      this.$refs.news && this.$refs.news.switchType(arg);
      this.sw = '';
      this.handleBlurInput();
    },
    handleBlurInput() {
      const inputElement =
        this.$refs.inputRef &&
        this.$refs.inputRef.querySelector('.van-field__control');
      if (inputElement) {
        inputElement.blur();
      }
    },
    doSearch() {
      if (!this.sw) {
        if (!this.searchFallback) {
          // toast().tip('请输入关键词');
          // return;
        } else {
          this.sw = this.searchFallback;
        }
      }
      this.searchFallback = '';
      this.searchTime = new Date().getTime();
      this.searchIndex = this.newsType;
      this.$nextTick(() => {
        // this.$refs.news && this.$refs.news.doSearch();
      });
    },
    inputFocus() {
      // this.inSearchMode = true;
    },
    inputChange(val) {},
    onResume() {
      console.log('home resume...');
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color: #ebebeb;

$menu-border-color: #dadada;
.container-news ::v-deep {
  .swiper-container {
    // margin-top: 10px;
    display: flex;
    flex: 1;
  }
  .swiper-wrapper {
    flex: 1;
    height: auto;
  }
  .swiper-slide {
    display: flex;
    height: inherit;
  }
  .news-panel {
    padding: 5px 10px;
    background: white;
    min-height: 100px;
  }
  background: #f3f3f3 url(../forum/assets/images/top-bg.png) center top
    no-repeat;
  background-size: 100% auto;
  .tab-container,
  .car-tech {
    background: transparent !important;
  }
  .content-wrapper {
    height: calc(100vh - 44px);
  }
  .news-tabs {
    padding: 10px 7px;
    box-sizing: border-box;
    .tab-items {
      border-bottom: none;
      .tab-item {
        font-size: 14px;
        color: #666666;
        line-height: 18px;
      }
      .tab-item-active {
        color: #fd4925;
        font-weight: bold;
        &::after {
          display: none;
        }
      }
    }
  }
  .swiper-slide > div {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }
  .panel-content {
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    .news-item {
      border-radius: 10px;
      background: #fff;
      margin-bottom: 10px;
      padding: 15px;
      box-sizing: border-box;
      width: 100%;
      overflow: hidden;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.news-tabs {
  // background: white;
  // box-shadow: 0 0 0 0 #dcdcdc; /* px */
  z-index: 1;
}
.error {
  text-align: center;
  padding: 10px;
  margin: 20px;
  &::before {
    content: '\E64f';
    font-family: iconfont;
    display: block;
    font-size: 38px;
    color: #3a3a3a;
  }
}
.error {
  margin-top: 30%;
}
.van-search {
  // padding: 0;
  text-align: center;
  font-size: 18px;
  margin: 0;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 1px;
  user-select: none;
  .van-search__content {
    border-radius: 17px;
  }
}
.news-page-content {
  margin-top: 45px;
  user-select: none;
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
}
.no-header {
  .news-page-content {
    margin-top: 0;
  }
}
</style>
