<template>
  <container
    class="consumer-benefits"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>
      <div slot="right" class="share">
        <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
      </div>
    </x-header>
    <content-view ref="content" :status="status" @reload="reload">
      <div class="content-wrap">
        <div class="banner"></div>
        <div class="content">
          <div class="benefits-title">权益内容</div>
          <div class="benefits-list">
            <div
              class="benefit-card"
              v-for="(item, index) in benefitsList"
              :key="index"
            >
              <div class="card-left">
                <i class="check-icon"></i>
                <div class="card-info">
                  <div class="card-title">{{ item.cardName }}</div>
                  <div class="card-date">
                    有效期至{{ handleDate(item.validDays) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <van-button
            class="receive-btn"
            block
            round
            :loading="loading"
            :disabled="disabled"
            @click="receiveBenefits"
          >
            一键领取
          </van-button>
          <div class="activity-date">
            活动有效期：{{ formatDate(pageData.sdate) }}至{{
              formatDate(pageData.edate)
            }}
          </div>
        </div>
        <div class="activity-rules">
          <h3>活动规则</h3>
          <div class="rules-content" v-html="pageData.description"></div>
        </div>
      </div>
      <PrizePopCard
        v-if="showPrize"
        :show="showPrize"
        :prize="prizeInfo"
        @cancel="handleTipsClose"
        @confirm="handleTipsConfirm"
      ></PrizePopCard>
    </content-view>
  </container>
</template>

<script>
import { AppStatus } from '@/enums';
import { mixinAuthRouter, mixinShare } from '@/mixins';
import { formatDate, getAppURL } from '@/utils';
import { getImageURL } from '@/common/image';
import { toast } from '@/bus';
import { Button, Dialog } from 'vant';
import { getGrantSecondaryCardActionDetail, receiveSecondaryCard } from './api';
import PrizePopCard from './components/PrizePopCard.vue';

export default {
  name: 'ConsumerBenefits',
  mixins: [mixinAuthRouter, mixinShare],
  components: {
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    PrizePopCard,
  },
  data() {
    let actionId = this.$route.query.id;
    return {
      AppStatus,
      status: AppStatus.LOADING,
      pageTitle: '消费权益领取',
      keepAlive: false,
      loading: false,
      disabled: false,
      pageData: {},
      benefitsList: [
        {
          title: '会员权益',
          expireDate: '2025-05-01',
          category: 'car',
        },
      ],
      actionId,
      showPrize: false,
      prizeInfo: {},
    };
  },
  methods: {
    formatDate(date, format = 'YYYY-MM-DD') {
      return formatDate(date, format);
    },
    handleDate(date) {
      return this.formatDate(
        new Date(new Date().getTime() + date * 24 * 60 * 60 * 1000).getTime()
      );
    },
    init() {
      this.status = AppStatus.READY;
      // 初始化数据
      getGrantSecondaryCardActionDetail(this.actionId)
        .then(res => {
          this.pageData = res;
          this.pageTitle = res.title || '消费权益领取';
          this.benefitsList = res.grantSecondaryCardInfos || [];

          // 判断活动是否结束
          if (
            new Date(res.edate).getTime() < new Date().getTime() ||
            res.status == -1
          ) {
            this.disabled = true;
            toast().tip('活动已下线');
          } else if (res.status == 0) {
            this.disabled = true;
            toast().tip('活动未开始');
          } else {
            this.disabled = false;
          }
          this.status = AppStatus.READY;
          this.share();
        })
        .catch(err => {
          this.status = AppStatus.ERROR;
          toast().tip(err.message);
        });
    },
    receiveBenefits() {
      if (this.loading || this.disabled) return;

      this.loading = true;
      receiveSecondaryCard({
        id: this.actionId,
      })
        .then(res => {
          this.loading = false;
          this.disabled = true;
          toast().tip('领取成功');
          this.showPrizeCard({
            prizeName: `恭喜您获得${this.benefitsList.length}张次卡`,
          });
        })
        .catch(err => {
          this.loading = false;
          toast().tip(err.message);
        });
    },
    showPrizeCard(prize) {
      this.prizeInfo = prize;
      this.showPrize = true;
    },
    handleTipsClose() {
      this.showPrize = false;
    },
    handleTipsConfirm() {
      this.showPrize = false;
      this.$_auth_push('/account/cards');
    },
    share(action = 'config') {
      const title = this.pageData.shareTitle;
      const logo = getImageURL(this.pageData.shareImage);
      const desc = this.pageData.shareContent;
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      this.init();
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.consumer-benefits {
  min-height: 100vh;
  background: #f5f5f5;

  .content-wrap {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }
  .banner {
    width: 100%;
    height: 125px;
    background: url('./assets/images/consumer-benefits.png') no-repeat left top;
    background-size: 100% auto;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
  }

  .content {
    padding: 20px 15px;
    border-radius: 12px 12px 0px 0px;
    margin-top: -10px;
    background: #fff;
    position: relative;
    z-index: 1;

    .benefits-title {
      width: 154px;
      height: 30px;
      line-height: 30px;
      background: linear-gradient(90deg, #ff9582 0%, #fd4b35 100%);
      border-radius: 0px 0px 35px 35px;
      font-size: 15px;
      color: #ffffff;
      text-align: center;
      margin: -20px auto 20px;
    }
    .benefits-list {
      padding: 12px;
      background: #ffd5d5;
      border-radius: 8px 8px 8px 8px;
      .benefit-card {
        background: #fff;
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 15px;

        .card-left {
          display: flex;
          align-items: center;

          .check-icon {
            flex-shrink: 0;
            word-break: keep-all;
            width: 24px;
            height: 24px;
            background: url('./assets/images/check.png') no-repeat center;
            background-size: 100% 100%;
            margin-right: 12px;
          }

          .card-info {
            width: 0;
            flex: 1;

            .card-title {
              font-size: 15px;
              color: #111111;
              font-weight: 500;
              margin-bottom: 4px;
            }

            .card-date {
              font-size: 12px;
              color: #999999;
            }
          }
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .receive-btn {
      width: 288px;
      height: 42px;
      box-sizing: border-box;
      font-size: 15px;
      font-weight: 500;
      background: $lh-2022-primary-color;
      border: none;
      margin: 15px auto;
      color: #fff;

      &--loading {
        opacity: 0.8;
      }

      &--disabled {
        background: #ccc;
      }
    }

    .activity-date {
      text-align: center;
      font-size: 13px;
      color: #999999;
    }
  }

  .activity-rules {
    flex: 1;
    margin-top: 12px;
    padding: 20px 15px;
    background: #fff;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .rules-content {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }

  .share {
    width: 45px;
    height: 45px;
    text-align: center;
    line-height: 46px;
    .icon_jglh {
      font-size: 24px;
    }
  }
}
</style>
