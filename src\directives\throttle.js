/*
  需求：防止按钮在短时间内被多次点击，使用节流函数限制规定时间内只能点击一次。
  思路：
    1、第一次点击，立即调用方法并禁用按钮，等延迟结束再次激活按钮
    2、将需要触发的方法绑定在指令上

  使用：给 Dom 加上 v-throttle 及回调函数即可
  <button v-throttle="debounceClick">节流提交</button>
*/
export default {
  name: 'throttle',
  install(Vue, defaultOptions = {}) {
    Vue.directive('throttle', {
      bind: function (el, binding) {
        if (typeof binding.value !== 'function') {
          throw new Error('指令callback must be a function');
        }
        let timer = null;
        el.__handleClick__ = function () {
          if (timer) {
            clearTimeout(timer);
          }
          if (!el.disabled) {
            el.disabled = true;
            binding.value();
            timer = setTimeout(() => {
              el.disabled = false;
            }, 1000);
          }
        };
        el.addEventListener('click', el.__handleClick__);
      },
      inserted: function (el, binding) {

      },
      update: function () {},
      componentUpdated: function () {},
      unbind: function (el, binding) {
        el.removeEventListener('click', el.__handleClick__);
      }
    });
  }
};
