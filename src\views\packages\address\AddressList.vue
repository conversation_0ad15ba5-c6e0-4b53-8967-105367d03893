<template>
  <container
    class="account-address"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
  >
    <x-header :title="pageTitle">
      <x-button slot="left" type="back"></x-button>

      <!-- <x-button v-if="mode === PAGE_MODE.SELECT" slot="right" @click="setPageMode(PAGE_MODE.EDIT)">管理</x-button>
      <x-button v-else-if="mode == PAGE_MODE.EDIT" slot="right" @click="setPageMode(PAGE_MODE.SELECT)">完成</x-button> -->
    </x-header>

    <content-view
      :status="status"
      :refreshAction="refreshAction"
      @refresh="refresh"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <div
          v-if="mode === PAGE_MODE.SELECT && list.length > 0"
          class="select-tips"
        >
          点击地址行即可选中并自动返回
        </div>
        <div class="address-list">
          <van-radio-group v-model="checked" @change="handleChange">
            <div
              class="address-item"
              v-for="(item, index) in list"
              :key="index"
              @click="selectAddress(item)"
            >
              <div
                class="address-content"
                @click.stop="handleAddressContentClick(item)"
              >
                <h4 class="address-title">
                  <span class="address-attr address-user">{{
                    item.recvName
                  }}</span>
                  <span class="address-attr address-phone">{{
                    item.phone
                  }}</span>
                  <span class="address-attr address-label" v-if="item.defaulted"
                    >默认地址</span
                  >
                  <span class="address-attr address-label" v-if="item.flag">{{
                    item.flag
                  }}</span>
                </h4>
                <div class="address-detail">
                  {{ item.province }}{{ item.city }}{{ item.county }}
                  {{ item.address }}
                </div>
              </div>
              <div class="address-action">
                <van-radio
                  class="default-checkbox"
                  :name="item.id"
                  checked-color="#FD4925"
                  icon-size="14px"
                  >设为默认</van-radio
                >
                <span class="address-btn" @click.stop="copyAddress(item)"
                  >复制</span
                >
                <span class="address-btn" @click.stop="editAddress(item)"
                  >编辑</span
                >
              </div>
            </div>
          </van-radio-group>
          <list-placeholder v-if="!list.length">没有记录</list-placeholder>
        </div>

        <div class="button-sp-area" slot="foot">
          <a
            href="javascript:;"
            class="weui-btn weui-btn_primary"
            @click="goAddNewAddress"
            >新增收货地址</a
          >
          <!-- <a href="javascript:;" class="weui-btn weui-btn_plain-primary" @click="$_router_push4inputting('/account/address/add')">管理</a> -->
        </div>
      </template>
    </content-view>
  </container>
</template>
<script>
import { formatDate, copyToClipboard, captureExpectionWithData } from '@/utils';
import { AppStatus } from '@/enums';
import { getAddressList, removeAddress, setAddressAsDefault } from '@/api';
import { mixinAuthRouter } from '@/mixins';
import { back, loading, toast, dialog, selectAddress } from '@/bus';
import { RadioGroup, Radio } from 'vant';

const PAGE_MODE = {
  SELECT: 'select',
  EDIT: 'edit',
  LIST: 'list',
};

function getInitialData(mode = PAGE_MODE.LIST) {
  return {
    AppStatus,
    PAGE_MODE,
    status: AppStatus.LOADING,
    mode,
    refreshAction: 1,
    pageTitle: '收货地址',
    page: {
      list: [],
    },
    checked: '',
  };
}

export default {
  name: 'address-select',
  components: {
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
  },
  mixins: [mixinAuthRouter],
  data() {
    return getInitialData(this.$route.params.mode);
  },
  computed: {
    list() {
      return this.page.list;
    },
  },
  mounted() {},
  methods: {
    initPageData() {
      return getAddressList().then(
        list => {
          this.page.list = list;
          // 将list中defaulted是true的id取出，放到checked中
          const defaultItem = list.filter(item => item.defaulted)[0];
          this.checked = defaultItem ? defaultItem.id : '';
          this.status = AppStatus.READY;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    init() {
      return this.initPageData();
    },
    refresh() {
      this.initPageData()
        .then(() => {
          this.refreshAction = Date.now();
        })
        .catch(e => {
          toast().tip('刷新失败');
          this.refreshAction = Date.now();
        });
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.init();
    },
    onResume() {
      this.initPageData();
    },
    setPageMode(mode) {
      this.mode = mode;
    },
    selectAddress(item) {
      if (this.mode == PAGE_MODE.SELECT) {
        selectAddress(item);
        back();
      }
    },
    handleAddressContentClick(item) {
      if (this.mode == PAGE_MODE.SELECT) {
        selectAddress(item);
        back();
      } else {
        this.editAddress(item);
      }
    },
    handleChange(val) {
      let item = this.page.list.filter(item => item.id == val)[0];
      this.setDefault(item);
    },
    copyAddress(item) {
      copyToClipboard(
        `收件人：${item.recvName}\n手机号：${item.phone}\n所在地区：${item.province}${item.city}${item.county}\n详细地址：${item.address}`
      )
        .then(res => {
          this.$toast('复制成功');
        })
        .catch(err => {
          captureExpectionWithData('复制洗车卡兑换码', {
            err,
          });
        });
    },
    editAddress(item) {
      this.$_router_push4inputting(`/account/address/${item.id}/edit`);
    },
    removeItem(id) {
      const that = this;
      dialog().confirm('您确认删除该地址吗？', {
        title: '提示',
        ok() {
          loading(true, '正在提交...');
          removeAddress(id).then(
            res => {
              loading(false);
              toast().success('已删除');
              that.onResume();
            },
            err => {
              loading(false);
              err &&
                dialog().alert(err, {
                  title: '',
                });
            }
          );
        },
      });
    },
    goAddNewAddress() {
      this.$_router_push4inputting(
        `/account/address/add?added=${this.list.length}`
      );
    },
    setDefault(item) {
      if (item.defaulted) return;
      loading(true, '正在设置...');
      const id = item.id;
      setAddressAsDefault(id).then(
        res => {
          loading(false);
          toast().success('已设置');
          this.page.list.forEach(item => {
            item.defaulted = item.id === id;
          });
        },
        err => {
          loading(false);
          err &&
            dialog().alert(err, {
              title: '',
            });
        }
      );
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.account-address {
  .select-tips {
    padding: 15px 15px 0;
    font-size: 12px;
    color: #999;
    text-align: center;
  }
  .address-list {
    padding: 15px;
  }
  .address-item {
    display: flex;
    flex-direction: column;
    background: white;
    padding: 15px 10px;
    margin-bottom: 12px;
    border-radius: 10px;
    .address-content {
      flex: 1;
      // @include border-bottom(rgb(202, 202, 202), 'after', 'last-child');
    }
    .address-attr {
      margin-right: 8px;
    }
    .address-user {
      font-size: 1.1em;
      font-weight: 700;
    }
    .address-phone {
      font-weight: 700;
    }
    .address-title {
      font-weight: 700;
    }
    .address-detail {
      color: rgb(61, 61, 61);
      margin-top: 5px;
      font-size: 14px;
    }
    .address-action {
      // padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      // border-left: 1px solid #f1f1f1;
      // margin-left: 10px;
      margin-top: 8px;
      .address-btn {
        margin-left: 15px;
        color: gray;
        padding: 0 8px;
        font-size: 14px;
        background: #f5f5f5;
        border-radius: 30px;
      }
    }
    .address-label {
      font-size: 12px;
      color: white;
      background: rgba(253, 73, 37, 0.1);
      color: $lh-2022-primary-color;
      padding: 3px 8px;
      border-radius: 10px;
      font-weight: 400;
    }
  }
  .button-sp-area {
    width: 100%;
    bottom: 0;
    padding: 10px 0;
    text-align: center;
    // background: white;
    display: flex;
    > .weui-btn {
      // margin: 10px auto 5px;
      line-height: 2;
      border-radius: 30px;
      width: auto;
      display: inline-block;
      padding: 2px 80px;
    }
  }
  .default-checkbox {
    flex: auto;
    font-size: 14px;
  }
}
</style>
