<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color-gray: #e8e8e8;
.container-box {
  padding: 0 15px;
}
.header {
  background: none;
}
.bg-color {
  position: absolute;
  width: 100%;
  background: url(./assets/images/detail-bg.png) no-repeat;
  background-size: 100% 100%;
  top: 0;
  height: 100px;
}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}
.shop-car-mt {
  .shop-info {
    background: white;
    border-bottom: 1px solid $border-color-gray;
  }
}
.weui-check__disabled {
  color: gray;
  .service-label {
    color: gray;
    border-color: gray;
  }
  .service-life {
    color: gray;
  }
  .net-price {
    display: none;
  }
}
.weui-cells:after {
  display: none;
  border-radius: 10px;
}
svg.icon-level {
  width: 2em;
  font-size: 24px;
  vertical-align: -0.2em;
  &.icon-level-1 {
    color: #fc3f3d;
  }
  &.icon-level-2 {
    color: #fd7d7c;
  }
}
.shop-car-mt {
  .banners {
    height: 200px;
    background: #e4e4e4;
  }
}
.shop-info {
  background: white;
  border-bottom: 1px solid $border-color-gray;
}
.shop-info-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 999;
  color: rgb(255, 255, 255);
  box-sizing: border-box;
  /*background: url(~@/assets/images/shop/gradient.png) center center no-repeat rgba(0, 0, 0, 0.6);
    background-size:contain;*/
  padding: 2px 10px;
  &:after {
    content: '';
    height: 0;
    display: block;
    box-shadow: 1px 1px 50px 50px rgba(0, 0, 0, 0.6);
  }
}
.shop-name {
  font-weight: 500;
  font-size: 18px;
  line-height: 1.2;
}
.shop-sales {
  float: right;
  font-size: 0.8em;
  position: relative;
  top: 2px;
}
.shop-time {
  font-size: 0.9em;
  > i {
    color: #f7821f;
    font-style: normal;
  }
}
.shop-about {
  padding: 8px 0 8px 8px;
}
.shop-brand.picture {
  width: 25px;
  height: 25px;
  margin-right: 5px;
}
.shop-address {
  flex: 1;
  color: #424242;
  line-height: 1.4;
  /*border-right: 1px solid #e4e4e4;*/
  @include border-right('#e4e4e4', 'after');
  font-size: 0.92em;
  padding: 0 8px;
  &:active {
    background: rgba(208, 208, 208, 0.11);
  }
}
.shop-brands {
  font-size: 0.9em;
  margin-top: 2px;
  line-height: 25px;
}
.shop-about {
  /*.icon-ditu{
      font-size:18px;
    }*/
  &::before {
    /*display:none;*/
    content: '\e647';
    font-family: iconfont;
    font-size: 1.2em;
  }
}
.shop-nav {
  padding-left: 15px;
  &::before {
    content: '\e679';
    font-family: iconfont;
    font-size: 18px;
    padding-right: 5px;
    display: block;
    color: #4e85fb;
  }
}
.shop-phone {
  padding: 0 15px 0 17px;
  font-size: 24px;
  color: #4e85fb;
  &:active {
    background: rgba(128, 128, 128, 0.1);
  }
  &:before {
    content: '\e618';
    font-family: iconfont;
    font-size: 22px;
  }
}
.carbox {
  border-radius: 10px;
}
.entrace-block {
  padding: 20px 15px;
  background: rgb(255, 255, 255);
  margin: 10px 0;
  align-items: center;
  .entrance-name {
    color: #333;
    font-weight: bold;
    font-size: 16px;
  }
  .entrance-note {
    color: rgb(165, 165, 165);
    font-size: 0.9em;
  }
  .entrance-actions {
    &::after {
      content: '\E605';
      font-family: iconfont;
      float: right;
      color: rgb(128, 128, 128);
    }
  }
}
.entrace-categories {
  margin-top: 0;
  border-top: 0;
  padding: 10px 15px;
  border-radius: 0 0 10px 10px;
}
.categories {
  display: flex;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  li {
    border-radius: 5px;
    font-size: 12px;
    text-align: center;
    height: 25px;
    line-height: 25px;
    color: #111;
    padding: 0 6px;
    background: #f3f3f3;
    flex-shrink: 0;
    margin-right: 16px;
    // &.hot::after {
    //   background: url(./assets/images/hot.png) right center no-repeat;
    //   content: "";
    //   position: absolute;
    //   padding: 0;
    //   width: 30px;
    //   height: 20px;
    //   top: 0;
    //   right: 0;
    //   color: white;
    //   background-size: contain;
    // }
    &.active {
      background: #fd4925;
      color: white;
    }
  }
}
.addCar {
  width: 27px;
  height: 27px;
  background: #fd4925;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 27px;
}
.member {
  padding: 5px;
  margin-bottom: 10px;
  .member-avatar {
    width: 50px;
    height: 50px;
    margin-right: 5px;
  }
  .member-about {
    flex: 1;
  }
  .member-name {
    font-weight: 400;
  }
  .member-job {
    font-size: 0.9em;
    margin-left: 5px;
  }
  .member-desc {
    margin-top: 3px;
    font-size: 0.9em;
    color: gray;
    padding-left: 2px;
  }
}

.tab-container {
  background: white;
  border-radius: 10px 10px 0 0px;
}
::v-deep .tab-items {
  border-bottom-color: #eee;
}
::v-deep .tab-item {
  font-size: 16px;
  color: #999;
  padding: 15px;
  line-height: 16px;
  font-weight: bold;
  flex: inherit !important;
  &::after {
    display: none;
  }
}
::v-deep .tab-item-active {
  color: #fd4925;
  &::after {
    display: none;
  }
}

.car-logo {
  width: 50px;
  height: 50px;
  margin-right: 12px;
}
.scroll-body-content {
  -webkit-overflow-scrolling: touch;
}
.cw-panel {
  margin: 0;
  // border-top: 1px solid #e4e4e4;
  width: 100%;
  .weui-cells {
    background: transparent;
  }
  .weui-cell {
    padding: 0 0 5px 0;
    border-top: 0;
    &:first-child .weui-cell__bd {
      border-top: 0;
    }
  }
  .weui-check__label {
    margin: 0 0 10px 0;
    background: white;
    align-items: flex-start;
    color: rgb(154, 154, 154);
    border-radius: 10px;
  }
  .weui-cell__bd {
    border-top: 1px solid #f1f1f1;
    padding: 15px;
  }
  .weui-cells__title {
    border-bottom: 1px solid #efefef;
    color: gray;
    background: transparent;
    text-align: center;
    font-size: 0.9em;
  }
  .weui-cell__ft {
    padding: 0 12px;
    .weui-icon-checked {
      background: #fcfcfc;
      border: 1px solid #bdc1c8;
    }
  }
  .pkg-parts {
    border-top: 1px solid #efefef;
    padding-top: 10px;
    margin-top: 10px;
    display: none;
    .pkg-part-logo {
      width: 90px;
      height: 75px;
      background-color: #fff;
      margin-right: 10px;
      border-radius: 10px;
    }
    .part-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    .part-title {
      font-size: 15px;
      color: #111111;
      line-height: 18px;
    }
    .part-desc {
      margin-top: 10px;
      font-size: 11px;
      color: #666666;
      line-height: 18px;
    }
    li {
      margin: 0 0 5px 0;
      border-bottom: 1px solid rgb(245, 244, 244);
      // @include border-bottom(rgb(245, 244, 244), 'after');
      padding-bottom: 5px;
      &:last-child {
        border-bottom: 0;
      }
    }
  }
  .weui-cell__checked .pkg-parts {
    display: block;
  }
  .weui-cell__checked {
    color: #434343;
    .weui-icon-checked {
      background: #fd4925;
      border-color: #fd4925;
    }
  }
}
.service-attr,
.part-desc {
  white-space: pre-line;
}
.shop-mt-info ::v-deep {
  .panel-title {
    border-bottom: 0;
  }
  .panel-content {
    font-size: 0.9em;
    color: RGB(75, 75, 75);
    padding: 5px;
  }
}

.panel-cert {
  .picture {
    width: 24%;
    height: 85px;
    margin: 5px 1% 1% 0;
  }
}

.weui-btn-contact {
  width: 100%;
  border-radius: 0;
}
.shop-services {
  border: 1px solid rgb(239, 239, 239);
  border-width: 1px 0;
  // padding-top: 5px;
  margin-top: 10px;
}
.service-body {
  flex: 1;
  overflow-y: hidden;
  margin-bottom: 10px;
  width: 100%;
  .weui-cells__title {
    padding: 0;
    margin-top: 10px;
  }
  .weui-cells {
    margin-top: 0;
    // background: white;
  }
  .weui-cells:before {
    display: none;
  }
}
.scroll-body__comments {
  word-wrap: break-word;
  width: 100%;
  background: white;
  box-sizing: border-box;
}
pre {
  white-space: normal;
}
.scrollable {
  overflow-y: scroll;
}
.service-box {
  padding-bottom: 50px;
}
.service-content {
  font-weight: 400;
  font-size: 16px;
  flex: 1;
  & > * {
    margin-bottom: 5px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.service-name {
  color: #111;
  margin-bottom: 5px;
  font-weight: 700;
}
.service-tip {
  font-size: 0.8em;
  margin-top: 2px;
  color: #868686;
}
.service-attr {
  color: #666666;
  font-size: 0.8em;
}
.service-note {
  color: gray;
}

$label-color: #ff8400;
.service-label {
  color: $label-color;
  border: 1px solid $label-color;
  padding: 2px;
  border-radius: 3px;
  padding-right: 5px;
  overflow: hidden;
  font-size: 0.9em;
}
.service-life {
  color: #4e85fb;
  margin-left: 5px;
}
.c-top-title {
  .c-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.service-price {
  .rmb {
    font-weight: 700;
    font-size: 20px;
    color: #fd4925;
    &::before {
      font-size: 0.7em;
    }
    .weui-cell__checked & {
      color: #fd4925;
    }
  }
}

.price-highlight {
  display: flex;
  justify-content: center;
}
.icon-hui {
  color: #ff1000;
  font-size: 22px;
}

.service-types {
  width: 100px;
  text-align: center;
  // border-right: 1px solid rgb(241, 241, 241);
  background: rgb(241, 241, 241);
  color: #5f5f5f;
}
.service-types > li {
  padding: 10px 0;
  padding-left: 18px;
  position: relative;
  &::before {
    position: absolute;
    left: 0;
    top: 0;
    content: '';
    height: 100%;
    width: 3px;
    background: transparent;
  }
  &::after {
    font-family: iconfont;
    position: absolute;
    left: 6px;
    /*display:none;*/
  }
  /*.icon {
      width:18px;
      height:18px;
      fill:#939393;
    }
    &.px .icon {
      fill:#F7821F;
    }
    &.jx .icon {
      fill:#FF2400;
    }
    &:not(.active) .icon{
      fill:#939393;
    }*/
  &.px::after {
    content: '\e623';
    color: #ff8400;
  }
  &.jx::after {
    color: #ff2400;
    content: '\e7d9';
  }
  &:not(.active)::after {
    color: #a0a0a0;
  }
  &.active {
    background: white;
    color: #2f2f2f;
    font-weight: 500;
    &::before {
      background: #4e85fb;
      font-weight: 700;
    }
  }
}
.vip-banners {
  border-radius: 10px;
}
.price {
  margin-left: 5px;
}

@import '~styles/variable/global.scss';
.net-price {
  color: #fe861f;
  margin: 0;
  font-size: 18px;
}
.shop-price {
  margin: 0;
  font-size: 0.9em;
  text-decoration: line-through;
  color: gray;
  text-align: right;
}

.shop-bottom {
  position: fixed;
  opacity: 1;
  transition: transform 100ms;
  will-change: transform;
  height: 50px;
  line-height: 50px;
  .amount-tip {
    font-size: 0.9em;
  }
  .ask {
    font-weight: 400;
    padding: 5px;
    background: #fab443;
    color: white;
    border-radius: 50%;
    display: inline-block;
    height: 26px;
    width: 26px;
    text-align: center;
    line-height: 20px;
    box-sizing: border-box;
    font-size: 16px;
    transform: scale(0.8);
  }
  .rmb {
    font-size: 26px;
    &::before {
      font-size: 0.8em;
    }
    &.price-lt {
      font-size: 15px;
      color: #545454;
      text-decoration: line-through;
    }
  }
  .price-amount {
    font-size: 24px;
    // color: #FE8E33;
    margin-left: 10px;
    &::before {
      font-size: 0.7em;
    }
    &.price-amount-delete {
      position: relative;
      &::after {
        content: '';
        width: 100%;
        height: 1px;
        background: black;
        top: 50%;
        left: 0;
        position: absolute;
      }
    }
  }

  .vip-price {
    display: inline-block;
    margin-left: 5px;
    .vip-price__rmb {
      font-size: 18px;
      color: #fd903d;
      font-weight: 700;
      &::before {
        font-size: 0.8em;
      }
    }
    .vip-price__icon {
      background: #fd903d;
      color: white;
      padding: 2px 5px;
      margin-left: 3px;
      vertical-align: 2px;
      border-radius: 2px;
      font-size: 0.9em;
    }
  }
  &.shop-bottom-hidden {
    /*visibility: hidden;
      overflow:hidden;*/
    transform: translate3d(0, 100%, 0);
  }
}
.shop-home-view {
  .weui-actionsheet__cell > a {
    display: block;
    color: inherit;
  }
  .service-empty {
    padding: 20px;
  }
}

.bottom-left {
  flex: 1;
}
.bottom-right {
  width: 100px;
  line-height: 47px;
  text-align: center;
  // background: rgb(78, 133, 251);
  color: white;
  &.disabled {
    background: #c1c1c1;
    color: rgba(255, 255, 255, 0.78);
  }
  // &:active:not(.disabled) {
  //   background-color: rgba(78, 133, 251, 0.8);
  // }
}
.shop-btn-box {
  width: 345px;
  left: 15px;
  height: 44px;
  bottom: 20px;
}
</style>

<template>
  <container
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header ref="header" title="保养门店">
      <x-button slot="left" type="back"></x-button>
      <div v-if="isReady" slot="right">
        <div slot="right" class="share">
          <i class="icon_jglh icon-fenxiang1" @click="share('show')"></i>
        </div>
      </div>
    </x-header>
    <div class="bg-color"></div>

    <content-view
      class="shop-car-mt"
      ref="view"
      :status="status"
      @reload="reload"
      @scroll="onScroll"
      @scroll-bottom="doCommentsAction('loadmore')"
    >
      <template v-if="status == AppStatus.READY">
        <div class="container-box">
          <x-shop-head
            :shop="shopHeadData"
            @event="handleHeadEvent"
          ></x-shop-head>
          <!-- <div class="banner-vip" style="margin: 5px auto; text-align: center;" @click="$_route_vipPage">
            <c-picture style="width: 375px; height: 65px;" src="@/assets/images/banners/vip-nav3.png"></c-picture>
          </div> -->
          <!-- <vip-banner
            class="vip-banners"
            v-if="!$_auth_isCarVip"
            :vip="$_auth_isCarVip"
            @click="$_route_mtCardPage"
            :banners="[
              require('@pkg/maintain/assets/images/banner-mtcard-1.png')
            ]"
          >
          </vip-banner> -->

          <template v-if="$_auth_isLoggedIn">
            <!-- 有车型信息无车牌号 -->
            <div
              v-if="car && car.carModelInfo"
              class="entrace-block flex-row carbox"
              @click="changeMyCar"
            >
              <c-picture
                class="car-logo"
                :src="car.carModelInfo.brandLogo"
                :placeholder="require('./assets/images/icon-car.png')"
              >
              </c-picture>
              <div class="flex-item entrance-content">
                <h4 class="entrance-name">
                  {{ car.carRegistNumber || '请完善车牌号' }}
                </h4>
                <p class="entrance-note">
                  <span>{{ car.carModelInfo.carSysName }}</span>
                </p>
              </div>
              <div class="flex-col center entrance-actions"></div>
            </div>

            <!-- 无车型信息有车牌号 -->
            <div
              v-else-if="car && !car.carModelInfo && car.carRegistNumber"
              class="entrace-block flex-row carbox"
              @click="changeMyCar"
            >
              <c-picture
                class="car-logo"
                :placeholder="require('./assets/images/icon-car.png')"
              ></c-picture>
              <div class="flex-item entrance-content">
                <h4 class="entrance-name">请完善爱车信息</h4>
                <p class="entrance-note">
                  <span>{{ car.carRegistNumber }}</span>
                </p>
              </div>
              <!-- <div class="flex-col center entrance-actions"></div> -->
              <div class="addCar">
                <van-icon name="plus" />
              </div>
            </div>
            <div
              v-else
              class="entrace-block flex-row carbox"
              @click="changeMyCar"
            >
              <c-picture
                class="car-logo"
                :placeholder="require('./assets/images/icon-car.png')"
              ></c-picture>
              <div class="flex-item entrance-content">
                <h4 class="entrance-name">无车辆信息</h4>
                <p class="entrance-note">
                  <span>请添加爱车信息</span>
                </p>
              </div>
              <!-- <div class="flex-col center entrance-actions"></div> -->
              <div class="addCar">
                <van-icon name="plus" />
              </div>
            </div>
          </template>

          <div
            class="shop-services flex-col"
            v-scroll-view2="$refs.view.getScroller()"
          >
            <tabs
              ref="tabs"
              data-role="scroll-head"
              v-model="viewType"
              :tabs="viewTypes"
              :sticky="true"
            ></tabs>

            <div class="slider" data-role="scroll-body" ref="scroll-view">
              <div
                v-show="viewType === ViewType.A"
                ref="scroll-body__service"
                class="scroll-body-content scroll-body__service service-box"
              >
                <div
                  v-if="packages.length"
                  class="flex-row entrace-block entrace-categories"
                >
                  <ul class="categories">
                    <li
                      v-for="(item, index) in packages"
                      :data-count="item.pkgs.length"
                      :key="index"
                      @click="setCategory(item.id)"
                      :class="{ active: category == item.id, hot: item.hot }"
                    >
                      <span>{{ item.name }}</span>
                    </li>
                  </ul>
                </div>
                <div class="service-body">
                  <div class="cw-panel">
                    <!-- <div class="weui-cells__title title-line">
                        <span>{{category.name}}</span>
                    </div> -->
                    <div class="weui-cells weui-cells_radio">
                      <label
                        v-for="(item, index) in currentPackages"
                        :key="index"
                        class="weui-cell weui-check__label"
                        :class="{ 'weui-cell__checked': form.pkg == item }"
                        @click="setPackage(item)"
                      >
                        <div class="weui-cell__bd center">
                          <div class="c-top-title">
                            <div class="c-select">
                              <div class="service-price flex-col">
                                <span class="rmb">{{ item.price }}</span>
                              </div>
                              <div class="weui-cell__ft">
                                <input
                                  type="radio"
                                  class="weui-check"
                                  name="package"
                                  v-model="form.pkg"
                                  :value="item"
                                  :id="'x' + index"
                                />
                                <span class="weui-icon-checked"></span>
                              </div>
                            </div>
                            <div class="service-content">
                              <h4 class="service-name">
                                <span>{{ item.name }}</span>
                              </h4>
                              <div class="service-attr">{{ item.desc }}</div>
                            </div>
                          </div>

                          <ul class="pkg-parts">
                            <li
                              v-for="(part, key) in item.parts"
                              :key="key"
                              class="flex-row"
                            >
                              <c-picture
                                class="pkg-part-logo"
                                :src="part.image"
                                type="?imageView2/1/w/180/h/150/format/jpg/q/80"
                              ></c-picture>
                              <div class="part-content">
                                <div class="part-title">{{ part.name }}</div>
                                <p class="part-desc">{{ part.desc }}</p>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </label>
                      <list-placeholder v-if="!currentPackages.length"
                        >暂无养车服务</list-placeholder
                      >
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-show="viewType === ViewType.B"
                class="scroll-body-content scroll-body__shop"
              >
                <panel title="简介">
                  <!-- <div v-html="shop.businessDesc || '暂无'"></div> -->
                  <content-fold
                    :rows="5"
                    :changer="viewType"
                    :value="shop.businessDesc"
                    placeholder="暂无"
                  ></content-fold>
                </panel>

                <panel title="承诺">
                  <!-- <div v-html="shop.businessPromise"></div> -->
                  <content-fold
                    :rows="5"
                    :changer="viewType"
                    :value="shop.businessPromise"
                    placeholder="暂无"
                  ></content-fold>
                </panel>

                <panel title="团队">
                  <template
                    v-if="shop.businessTeams && shop.businessTeams.length"
                  >
                    <div
                      v-for="(item, index) in shop.businessTeams"
                      :key="index"
                      class="member flex-row"
                    >
                      <c-picture
                        class="member-avatar"
                        :src="item.image"
                      ></c-picture>
                      <div class="member-about">
                        <h3 class="member-name">
                          {{ item.name }}
                          <span class="member-job">{{ item.job }}</span>
                        </h3>
                        <p class="member-desc">{{ item.desc }}</p>
                      </div>
                    </div>
                  </template>
                  <div v-else>暂无</div>
                </panel>

                <panel class="panel-cert" title="资质">
                  <template v-if="shop.certificate && shop.certificate.length">
                    <c-picture
                      v-for="(item, index) in shop.certificate"
                      :key="index"
                      :src="item"
                      @click="playPhotos(shop.certificate, index)"
                    ></c-picture>
                  </template>
                  <div v-else class="cert-empty">暂无</div>
                </panel>

                <div
                  v-if="shop.opendStatus == ShopLevel.PRIMARY"
                  slot="foot"
                  class="fixed-bottom shop-bottom flex-row"
                >
                  <a
                    href="javascript:;"
                    class="weui-btn weui-btn_primary weui-btn-contact"
                    @click="callPhone(shop.phone)"
                    >预约咨询</a
                  >
                </div>
              </div>

              <div
                v-show="viewType === ViewType.C"
                ref="scroll-body__comments"
                class="scroll-body-content scroll-body__comments"
              >
                <shop-comments
                  @error="onCommentsLoadError"
                  ref="comments"
                  :scroll-target="$refs['view'].getScroller()"
                  :id="comments.shopId"
                  :type="comments.type"
                >
                </shop-comments>
              </div>
            </div>
          </div>

          <!-- <div
            slot="foot"
            v-if="viewType === ViewType.A"
            ref="foot"
            class="fixed-bottom shop-bottom flex-row shop-btn-box"
            :class="{ 'shop-bottom-hidden': hideBuyButton || showShopContact }"
          > -->

          <transition
            name="actionsheet"
            @enter="
              () => {
                this.showShopContact = true;
              }
            "
            @beforeLeave="
              () => {
                this.showShopContact = false;
              }
            "
          >
            <router-view> </router-view>
          </transition>
        </div>
        <div
          slot="foot"
          v-if="viewType === ViewType.A"
          ref="foot"
          :class="{ 'shop-bottom-hidden': hideBuyButton || showShopContact }"
        >
          <template v-if="form.pkg">
            <layout-bottom
              slot="foot"
              :price="form.pkg.price"
              :vip-price="form.pkg.vipPrice"
              :vip="$_auth_isCarVip"
              :acceptStatus="shop.acceptStatus"
              :stopSubmitOrder="stopSubmitOrder"
              @go="submit"
            >
            </layout-bottom>

            <!-- <div class="bottom-left">
                <div class="checkout">
                  <b
                    class="rmb price-amount"
                    :class="{
                      'price-amount-delete':
                        $_auth_isCarVip && form.pkg.vipPrice < form.pkg.price
                    }"
                    v-text="form.pkg.price"
                  ></b>
                  <div
                    v-if="form.pkg.vipPrice && form.pkg.price > form.pkg.vipPrice"
                    @click="$_route_vipPage('car')"
                    class="vip-price"
                  >
                    <span
                      class="rmb vip-price__rmb"
                      v-text="form.pkg.vipPrice"
                    ></span>
                    <span class="vip-price__icon">VIP会员</span>
                  </div>
                </div>
              </div>
              <div
                v-if="shop.acceptStatus"
                class="bottom-right weui-btn weui-btn_primary"
                @click="buy(form.pkg.id)"
              >
                去结算
              </div>
              <div v-else class="bottom-right disabled">暂停接单</div> -->
          </template>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import {
  fixRichHtmlImageSize,
  formatShopHours,
  formatScore,
  getAppURL,
} from '@/utils';
import {
  AppStatus,
  ImageType,
  ShopLevel,
  ShopService,
  OrderType,
  OrderBizType,
} from '@/enums';
import { playPhotos, navigate } from '@/bridge';
import { isInWeixin } from '@/common/env';
import { getImageURL } from '@/common/image';
import { Panel, Rater, Tabs } from '@/components';
import { dialog, loading, toast, bind, Events } from '@/bus';
import { mixinAuthRouter, mixinShare, mixinScroll, mixinOrder } from '@/mixins';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import ContentFold from '@/components/ContentFold.vue';
import ShopComments from '@/views/_CommentList.vue';
import XShopHead from '@/views/components/ShopHead/ShopHead.vue';
import { Icon } from 'vant';
import LayoutBottom from './components/_LayoutBottom.vue';
import {
  getShopMaintenanceData,
  getShopMTPackages,
  getMyDefaultCar,
  getMyCar,
  submitCarMaintainCommonOrder,
  submitCarMaintainReserveOrder,
  recordPhoneCall,
} from '@/api';
import VipBanner from '@pkg/vehicle-business/components/_VipBanner.vue';

function getPageData(bid, withCarInfo) {
  const requests = [
    getShopMaintenanceData(bid),
    getShopMTPackages(bid),
    // getMyCar(car),
  ];
  if (withCarInfo) {
    requests.push(getMyDefaultCar());
  } /*  else {
      requests.push(Promise.resolve({}));
    } */
  return Promise.all(requests);
}

const ViewType = {
  A: 1,
  // B: 2,
  C: 3,
};

const ShopOpenStatus = {
  OPENING: 1,
};

export default {
  name: 'MaintainShopInfo',
  components: {
    'layout-bottom': LayoutBottom,
    Rater,
    Panel,
    Tabs,
    ShopComments,
    ContentFold,
    XShopHead,
    VipBanner,
    [Icon.name]: Icon,
  },
  mixins: [mixinAuthRouter, mixinShare, mixinScroll, mixinOrder],
  data() {
    const defaultCategory = this.$route.query.category || 0;
    return {
      AppStatus,
      ViewType,
      ShopLevel,
      ShopService,
      ImageType,
      status: AppStatus.LOADING,
      pageActive: true,
      hideBuyButton: false,
      showShopContact: false,
      stopSubmitOrder: false,
      servicePhone: '************',
      page: {},
      category: defaultCategory,
      viewType: ViewType.A,
      form: {
        car: 0,
        pkg: null,
      },
      comments: {
        shopId: 0,
        type: 'mt',
      },
      reachBottom: false,
      panelHeight: 'auto',
      viewTypes: [
        {
          name: '养车服务',
          value: ViewType.A,
        },
        {
          name: '用户评价',
          value: ViewType.C,
        },
        // {
        //   name: '商家信息',
        //   value: ViewType.B,
        // },
      ],
    };
  },
  mounted() {
    bind(Events.AFTER_CAR_CHANGED, car => {
      this.page.car = car;
      this.form.car = car.id;
    });
  },
  filters: {
    ...{ formatScore },
  },
  computed: {
    isReady() {
      return this.status == AppStatus.READY;
    },
    shopHeadData() {
      const shop = this.shop;
      // const startTime = formatShopHours(shop.serviceStartTime)
      // const endTime = formatShopHours(shop.serviceEndTime)
      return {
        name: shop.title,
        serviceStartTime: shop.serviceStartTime,
        serviceEndTime: shop.serviceEndTime,
        address: shop.address,
        logo: shop.logo,
        lat: shop.lat,
        lng: shop.lng,
        images: shop.images,
        commentCount: shop.mtCommentCount,
        commentScore: shop.mtScore,
      };
    },
    shopCommentsView() {
      return `/shop/${this.shop.id}/comment/mt`;
    },
    currentPackages() {
      if (!this.category) return [];
      const item = this.packages.filter(item => item.id === this.category)[0];
      if (!item) return [];
      return item.pkgs;
    },
    packages() {
      return this.page.packages
        .map(item => {
          return {
            id: item.id,
            name: item.name,
            hot: item.name === '嘉实多·品质换油',
            // 过滤，没有配件的套餐不予显示
            pkgs: item.planList
              .filter(v => v.partsList)
              .map(item2 => {
                return {
                  id: item2.id,
                  name: item2.schemeName,
                  status: item2.schemeStatus,
                  price: item2.price,
                  vipPrice: item2.vipPrice,
                  desc: item2.describe,
                  category: item.id,
                  sort: item2.sort,
                  parts: item2.partsList
                    .filter(v => v.status)
                    .map(item => {
                      return {
                        id: item.schemeId,
                        name: item.name,
                        desc: item.describe,
                        image: item.image,
                        status: item.status,
                      };
                    }),
                };
              })
              .sort((a, b) => {
                // 服务器端未对返回结果做排序处理，前端临时做兼容
                return b.sort - a.sort;
              }),
          };
        })
        .filter(v => v.pkgs.length);
    },
    shop() {
      // 春节期间店铺默认为暂停接单状态
      const shouldTip =
        Date.now() > new Date(2023, 1 - 1, 17, 0, 0, 0) &&
        Date.now() < new Date(2023, 1 - 1, 31, 0, 0, 0);
      if (shouldTip) {
        this.page.shop.acceptStatus = 0;
      }
      return this.page.shop;
    },
    car() {
      return this.page.car;
    },
    panelStyle() {
      const panelHeight = this.panelHeight;
      return {
        height: `${panelHeight}px`,
      };
    },
    serviceHours() {
      const shop = this.shop;
      const startTime = formatShopHours(shop.serviceStartTime);
      const endTime = formatShopHours(shop.serviceEndTime);
      return `${startTime} ~ ${endTime}`;
    },
  },
  watch: {
    viewType(val, oldVal) {
      if (val === ViewType.C) {
        this.doCommentsAction('init');
      }
    },
  },
  methods: {
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      // this.getPageData();
      // this.$_auth_checkSession(true)
      if (this.$_auth_isLoggedIn && this.car && !this.car.carRegistNumber) {
        getMyCar(this.car.id)
          .then(res => {
            this.page.car = res;
          })
          .catch(e => {
            toast().tip(String(e));
          });
      }
      // this.status = AppStatus.READY;
    },
    setCategory(category) {
      this.category = category;
      this.$nextTick(() => {
        this.form.pkg = this.currentPackages[0];
      });
    },
    setPackage(item) {
      this.form.pkg = item;
    },
    setDefaultData() {
      const defaultCategory = this.category;
      const defPkg = this.packages.reduce((prev, item) => {
        return item.id == defaultCategory ? item : prev;
      }, this.packages[0]);
      if (defPkg) {
        this.category = defPkg.id;
        this.form.pkg = defPkg.pkgs[0];
      } else {
        this.hideBuyButton = true;
      }
    },
    getPageData(withCarInfo = this.$_auth_isLoggedIn) {
      const shopId = this.$route.params.id;
      this.comments.shopId = shopId;
      // toast().tip('auth1:' + this.$_auth_isLoggedIn);
      return getPageData(shopId, withCarInfo)
        .then(res => {
          const [shop, packages, car] = res;
          this.page = { shop, packages, car };
          if (car) {
            this.form.car = car.id;
          }
          // debugger
          this.setDefaultData();
          this.status = AppStatus.READY;
        })
        .catch(err => {
          console.error(err);
          this.status = AppStatus.ERROR;
          return Promise.reject(err);
        });
    },
    init() {
      this.getPageData().then(() => {
        // const shouldTip =
        //   Date.now() > new Date(2024, 1, 6, 0, 0, 0) &&
        //   Date.now() < new Date(2024, 1, 17, 0, 0, 0);
        const shouldTip = this.checkTime();
        this.stopSubmitOrder = shouldTip;
        if (shouldTip) {
          this.shop.acceptStatus = 0;
        }
      });
      // if (Date.now() < new Date(2022, 2 - 1, 15, 0, 0, 0)) {
      //   dialog().alert('春节期间，进店前请先与对应门店联系确认是否营业', {
      //     title: '温馨提示'
      //   });
      // }
    },
    checkTime() {
      const { available, message } = checkServiceStatus(
        ServiceEnum.MAINTENANCE
      );
      if (!available) {
        dialog().alert(message, {
          title: '通知',
        });
      }
      return !available;
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.getPageData();
    },
    // 更换爱车
    changeMyCar() {
      // let url = this.car ? `/mt/car/select/${this.car.id}` : '/mt/car/select/';
      let url = '/mt/account/car';
      if (this.car && (!this.car.carRegistNumber || !this.car.carModelInfo)) {
        url = `/account/car/${this.car.id}/edit`;
      }
      this.$_router_push(url);
    },
    setCWType(t) {
      this.cwType = t;
    },
    setSliderAutoplay(flag) {
      this.$refs.slider && this.$refs.slider.setAutoplay(flag);
    },
    doCommentsAction(action) {
      if (this.$refs.comments) {
        if (action === 'init') {
          if (this.$refs.comments.loaded) return;
          this.$refs.comments.init();
          this.$refs.comments.loaded = true;
        }
        if (action === 'loadmore') {
          this.$refs.comments.loadMore();
        }
        if (action === 'scroll') {
          this.$refs.comments.onScroll();
        }
      }
    },
    handleHeadEvent(e) {
      if (e === 'show-contacts') {
        this.showContacts();
      }
    },
    goNavigate() {
      const { address, lat, lng } = this.shop;
      navigate({
        address,
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    },
    showContacts() {
      this.$router.push({
        name: 'mt4/contacts',
        params: {
          list: [
            {
              name: '商家电话',
              value: this.shop.phone,
            },
          ],
        },
      });
    },
    goToReserve() {
      const shop = this.$route.params.id;
      const car = this.$route.params.car;
      const service = this.checkedService.id;
      const url = `/shop/${shop}/service/${service}/car/${car}`;
      this.$_router_push(url);
    },

    buy2(service) {
      const { id: shop, car } = this.$route.params;
      loading(true, '正在提交...');

      let submiter;
      if (service === -1) {
        submiter = submitCarMaintainCommonOrder(shop, car);
      } else {
        submiter = submitCarMaintainReserveOrder(service, shop, car);
      }
      submiter.then(
        res => {
          loading(false);
          const url = `/order/pay/${res.oid}`;
          this.$router.push(url);
        },
        err => {
          loading(false);
          err &&
            dialog().alert(err, {
              title: '错误',
            });
        }
      );
    },
    submit(checked) {
      this.buy(checked, this.form.pkg.id);
    },
    buy(checked, service) {
      console.log(checked, service);
      const that = this;
      if (!this.$_auth_isLoggedIn) {
        this.$_auth_login().then(res => {
          loading(true);
          this.getPageData(true)
            .then(res => {
              loading(false);
            })
            .catch(err => {
              console.error(err);
              loading(false);
            });
        });
        return;
      }
      const { id: shop } = this.$route.params;
      // loading(true, '正在提交...');

      if (!this.car) {
        dialog().confirm('请先补充爱车资料！', {
          title: '提示',
          ok() {
            that.changeMyCar();
          },
        });
        return;
      }

      if (!this.car.carRegistNumber) {
        dialog().confirm('请先完善车牌号信息！', {
          title: '提示',
          ok() {
            that.changeMyCar();
          },
        });
        // toast().tip('请先完善车牌号信息！');
        return;
      }
      // 审车计算器会自动添加爱车，但没有车型信息，提交订单前需要确保车型信息的完善
      if (!this.car.carModelInfo) {
        dialog().confirm('请先补全车辆信息！', {
          title: '提示',
          ok() {
            that.changeMyCar();
          },
        });
        // toast().tip('请先完善车牌号信息！');
        return;
      }
      const form = {
        car: this.form.car,
        id: service,
        address: this.shop.address,
        joinVip: checked,
      };
      const goods = this.form.pkg;
      this.$_order_preSubmit({
        type: OrderBizType.CarMaintenance,
        resultUrlRender(oid) {
          return `/pay/result/${oid}`;
        },
        form,
        ticketQuery: {
          category: this.$_order_TicketCategory.CarMaintain,
          rid: shop,
        },
        // 合作渠道——微信社群营销
        cooperationChannel: this.$route.query.cooperationChannel,
        order: {
          title: this.shop.title,
          id: goods.id,
          name: goods.name,
          price: goods.price,
          mprice: goods.price,
          vipPrice: goods.vipPrice || goods.price,
        },
      });
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, ImageType.MEDIUM),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    showBuyTip() {
      toast().tip('此价格为参考价，到店后以实际保养所需费用为准');
    },
    playShopPhotos(index) {
      this.playPhotos(this.shop.images, index);
    },
    setService(item) {
      this.checkedService = item;
    },
    onScroll(top) {
      this.doCommentsAction('scroll');
      // this.updatePanelHeight();
    },
    clickTip(couldServe, isMatch2Car) {
      if (!couldServe) {
        const tip = isMatch2Car
          ? '此套餐与您的车型不匹配'
          : '商家暂未开通此业务，敬请期待';
        toast().tip(tip);
      }
    },
    share(action = 'update') {
      // 设置邀请链接分享信息
      const path = `/cm/shop/${this.shop.id}`;
      const link = getAppURL(path, {
        search: isInWeixin ? '?utm_source=WEIXIN&utm_medium=share' : '?jglh',
      });
      const shareInfo = {
        link: link,
        title: `交广养车-${this.shop.title}`,
        desc: this.shop.address,
        imgUrl: getImageURL(this.shop.logo),
      };
      if (action === 'show') {
        this.$_share(shareInfo);
      } else {
        this.$_share_update(shareInfo);
      }
    },
    onCommentsLoadError() {},
    callPhone(value) {
      // ga
      const label = `${this.shop.title}(${this.$route.params.id})`;
      this.$ua && this.$ua.trackEvent('Click', 'CallPhone', label, 1);
      // self server statistics
      recordPhoneCall(this.$route.params.id);
      window.open(`tel:${value}`);
    },
  },
};
</script>
