import { isPlainObject } from '@/utils';

/**
 * 适用于分页列表加载的页面
*/
export const mixinLoader = {
  data() {
    return {
      loader_data: {
        page: 1, // 页码，默认第一页
        size: 10, // 页大小，默认10条/页
        end: false, // 是否已到底
        loading: false, // 是否加载中
        error: false, // 是否加载出错了
      }
    }
  },
  computed: {
    $_loader_params() {
      // console.log('$_loaderD_data', this.loader_data);
      return {
        page: this.loader_data.page,
        rows: this.loader_data.size,
        pageSize: this.loader_data.size,
      }
    },
    $_loader_options() {
      const { end, loading, error } = this.loader_data;
      return {
        end,
        loading: loading,
        error: error,
      }
    },
    $_loader_couldLoadMore() {
      return !this.loader_data.end && !this.loader_data.loading;
    }
  },
  methods: {
    /**
     * 列表分页加载逻辑
     * @param {Promise} getter 数据源方法
     * @param {*} handler 数据返回后的处理函数
     * @param {*} options 配置参数
     */
    $_loader_bind(getter, handler, options = { pageName: 'page' }) {
      return {
        /**
         * load方法只支持一个参数
         * @param {object} params 参数对象，plain object
         */
        load: (params, params2) => {
          if (params2 || !isPlainObject(params)) {
            console.warn('load方法只识别第一个参数', params, params2)
          }
          this.loader_data.loading = true;
          return getter(params).then(handler).then(list => {
            this.$_loader_onListResponse(list);
            // 只对第一个入参做判断
            if (params) {
              const page = params[options.pageName];
              this.$_loader_setPage(page);
            }
            return list;
          }).catch(err => {
            this.$_loader_onListResponse();
            return Promise.reject(err);
          })
        }
      }
    },
    $_loader_onListResponse(list) {
      const isArray = list instanceof Array;
      this.loader_data.loading = false;
      this.loader_data.error = !isArray;
      if (isArray) {
        this.loader_data.error = false;
        this.loader_data.end = list.length < this.loader_data.size;
      }
    },
    $_loader_setPage(value) {
      if (value) {
        this.loader_data.page = value;
      }
    },
    $_loader_addPage(value = 1) {
      this.loader_data.page = this.loader_data.page + value;
    },
    $_loader_getPage() {
      return this.loader_data.page;
    },
    $_loader_setPageSize(value) {
      if (value) {
        this.loader_data.size = value;
      }
    },
  }
};
