<template>
  <div v-if="status == AppStatus.READY" class="emergency-wrap" ref="scrollWrap">
    <x-header title="一键应急">
      <x-button slot="left" type="back"></x-button>
      <span>一键应急</span>
    </x-header>
    <EmergencyMap class="map"></EmergencyMap>
    <div class="map-mask"></div>
    <div class="emergency-content" ref="content">
      <div v-if="settingInfo.length" class="card-nav">
        <div
          class="card-nav-item"
          v-for="item in settingInfo.slice(0, 2)"
          :key="item.id"
          @click="handleNavClick(item)"
        >
          <biz-image
            :src="item.image"
            :immediate="true"
            fill="cover"
            type="?"
            class="banner"
          >
          </biz-image>
        </div>
      </div>
      <template v-if="settingInfo.length > 2">
        <div
          v-for="item in settingInfo.slice(2)"
          :key="item.id"
          class="sms-help"
          @click="handleNavClick(item)"
        >
          <biz-image
            :src="item.image"
            :immediate="true"
            fill="cover"
            type="?"
            class="banner"
          >
          </biz-image>
        </div>
      </template>
      <div class="emg-contacts">
        <div class="emg-header">
          <div class="emg-header-title">应急联系人</div>
          <div
            class="emg-header-setting"
            @click="pageTo('/emergency/contactperson')"
          ></div>
        </div>
        <div v-if="contactList.length > 0" class="emg-list">
          <div class="emg-list-item" v-for="item in contactList" :key="item.id">
            <div class="emg-list-item-name">{{ item.contactName }}</div>
            <div class="emg-list-item-btn" @click="call(item.contactPhone)">
              一键呼叫
            </div>
          </div>
        </div>
        <div v-else class="emg-empty">
          <div class="empty-icon"></div>
          <div class="empty-text" @click="pageTo('/emergency/contactperson')">
            添加紧急联系人
          </div>
        </div>
      </div>
      <ContractCard v-for="item in classifyList" :key="item.id" :value="item" />
    </div>
    <SendSms :show="showSms" @close="showSms = false" />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { AppStatus } from '@/enums';
import { parseJglhURL, pushNativeView, callPhone, getVersion } from '@/bridge';
import { mixinAuthRouter } from '@/mixins';
import { mixinAuth } from '@/mixins/auth';
import { getImageURL } from '@/common/image';
import { isInJglh, isInWeixin, isInWeApp, isProduction } from '@/common/env';
import { toast, dialog } from '@/bus';
import {
  getEmergencySettingInfo,
  getEmergencyClassifyList,
  getEmergencyContactList,
} from './api';
import { Sticky } from 'vant';
import { throttle } from 'lodash';
import EmergencyMap from './components/Map.vue';
import ContractCard from './components/ContractCard.vue';
import SendSms from './components/SendSms.vue';
const MenuType = {
  TrafficSearch: 'traffic_search',
};
const permCodeEnums = {
  15: 'quick_help',
  16: 'emergency_sms',
  17: 'traffic_search',
};
export default {
  name: 'EmergencyAppIndex',
  components: {
    EmergencyMap,
    ContractCard,
    SendSms,
    [Sticky.name]: Sticky,
  },
  mixins: [mixinAuthRouter, mixinAuth],
  data() {
    return {
      AppStatus,
      isInJglh,
      map: null,
      content: null,
      status: AppStatus.LOADING,
      error: '',
      scrollTop: 0,
      settingInfo: [],
      classifyList: [],
      contactList: [],
      visibilityHandler: null,
      showSms: false,
    };
  },
  watch: {
    $route() {
      const scrollWrap = this.$refs.scrollWrap;
      if (!scrollWrap) return;
      scrollWrap.scrollTop = this.scrollTop;
    },
  },
  mounted() {
    this.init();
    this.content = this.$refs.content;
    const payload = {
      key: this.$options.name,
      value: this.$route.path,
    };
    this.addRoute(payload);

    // 添加页面可见性监听（兼容性处理）
    this.initVisibilityListener();
  },
  destroyed() {
    // 移除页面可见性监听（兼容性处理）
    this.removeVisibilityListener();
  },
  computed: {},
  methods: {
    ...mapActions(['addRoute']),
    call(phone) {
      callPhone(phone);
    },
    getImageURL(url) {
      return getImageURL(url);
    },
    getContactList() {
      getEmergencyContactList().then(res => {
        this.contactList = res;
      });
    },
    init() {
      // this.status = AppStatus.READY
      // 请求
      Promise.all([getEmergencySettingInfo(), getEmergencyClassifyList()]).then(
        res => {
          this.status = AppStatus.READY;
          this.settingInfo = res[0];
          this.classifyList = res[1];
          if (this.$_auth_isLoggedIn) {
            this.getContactList();
          }
        }
      );
    },
    pageTo(item) {
      if (item === MenuType.TrafficSearch) {
        pushNativeView({
          id: item,
        });
        return;
      }
      if (item == '/emergency/contactperson' && !this.$_auth_isLoggedIn) {
        this.$_auth_login();
        return;
      }
      const options = parseJglhURL(item);
      this.$_router_pageTo(options.url, {
        ...options,
        titleBar: false,
        progressBar: false,
      });
    },
    handleNavClick(item) {
      if (item.url) {
        this.$_router_pageTo(item.url, {
          titleBar: false,
          progressBar: false,
        });
        return;
      }
      if (item.permCode == 16) {
        this.showSms = true;
        return;
      }
      const appPageId = permCodeEnums[item.permCode];
      const jglhVersion = getVersion();
      if (appPageId && isInJglh && jglhVersion > 479) {
        pushNativeView({
          id: appPageId, // quick_help 一键应急页面480版本添加
        });
      } else {
        toast().tip('请下载最新版交广领航APP');
      }
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      this.init();
    },
    // 初始化可见性监听
    initVisibilityListener() {
      let hidden, visibilityChange;

      // 判断浏览器前缀
      if (typeof document.hidden !== 'undefined') {
        hidden = 'hidden';
        visibilityChange = 'visibilitychange';
      } else if (typeof document.webkitHidden !== 'undefined') {
        hidden = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
      }

      // 创建事件处理函数
      this.visibilityHandler = () => {
        // 检查页面是否可见
        const isHidden = document[hidden];
        if (!isHidden && this.$_auth_isLoggedIn) {
          this.getContactList();
        }
      };

      // 如果浏览器支持可见性API，添加监听器
      if (
        typeof document.addEventListener !== 'undefined' &&
        hidden !== undefined
      ) {
        document.addEventListener(
          visibilityChange,
          this.visibilityHandler,
          false
        );
      } else {
        // 降级处理：当浏览器不支持 Page Visibility API 时，使用 window.focus/blur 事件作为备选方案
        window.addEventListener(
          'focus',
          () => {
            if (this.$_auth_isLoggedIn) {
              this.getContactList();
            }
          },
          false
        );
      }
    },
    // 移除可见性监听
    removeVisibilityListener() {
      if (!this.visibilityHandler) return;

      ['visibilitychange', 'webkitvisibilitychange'].forEach(event => {
        document.removeEventListener(event, this.visibilityHandler, false);
      });

      window.removeEventListener('focus', this.visibilityHandler, false);
      this.visibilityHandler = null;
    },
  },
};
</script>
<style lang="scss" scoped>
@import '~styles/variable/global.scss';
.mb-15 {
  margin-bottom: 15px;
}
.bs-10 {
  border-radius: 10px;
  overflow: hidden;
}
::-webkit-scrollbar {
  display: none;
  background-color: transparent;
}
.emergency-wrap {
  width: 100%;
  height: 100%;
  overflow: auto;
  // pointer-events: none !important;
  background: #f1f1f4;
}
.map {
  position: fixed;
  top: 0;
  left: 0;
}
.map-mask {
  width: 100%;
  height: 40vh;
  // height: calc(230px + constant(safe-area-inset-top));
  // height: calc(230px + env(safe-area-inset-top));
  pointer-events: none;
  background: transparent;
}
.emergency-content ::v-deep {
  width: 100%;
  padding: 15px;
  border-radius: 16px 16px 0 0;
  box-sizing: border-box;
  position: relative;
  z-index: 2;
  background: transparent;
  // pointer-events: auto;
  // touch-action: auto;
  background: linear-gradient(to bottom, #ffffff, #f3f3f3 285px, #f3f3f3);
  border-radius: 10px 10px 0px 0px;
  .card-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 10px;
    .card-nav-item {
      flex: 1;
      text-align: center;
      // background-size: 100% auto;
      // background-repeat: no-repeat;
      // background-position: center;
      .card-nav-item-text {
        font-size: 14px;
      }
      .banner {
        width: 100%;
      }
    }
  }
  .sms-help {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 5px;
  }
  .emg-contacts {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 5px;
    background: #ffffff;
    .emg-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 1;
      padding: 15px;
      .emg-header-title {
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
      .emg-header-setting {
        font-size: 14px;
        width: 18px;
        height: 18px;
        background: url('@pkg/emergency/images/setting.png') no-repeat center
          center;
        background-size: 100% 100%;
      }
    }
    .emg-list {
      padding: 10px 15px 15px;
      line-height: 1;
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;

      .emg-list-item {
        display: inline-flex;
        flex-direction: column;
        justify-content: space-between;
        background: #ffffff;
        box-shadow: 0px 0px 5px 0px #eeeeee;
        border-radius: 5px;
        padding: 15px;
        margin-right: 15px;
        .emg-list-item-name {
          font-weight: bold;
          font-size: 15px;
          color: #333333;
          margin-bottom: 15px;
          text-align: center;
        }
        .emg-list-item-btn {
          font-weight: 500;
          font-size: 10px;
          color: #ffffff;
          padding: 0 15px;
          background: #fe4444;
          border-radius: 9px;
          height: 18px;
          line-height: 18px;
          text-align: center;
        }
      }
    }
    .emg-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px 15px 15px;
      .empty-icon {
        width: 100px;
        height: 65px;
        background: url('@pkg/emergency/images/contract.png') no-repeat center
          center;
        background-size: 100% 100%;
        margin-bottom: 5px;
      }
      .empty-text {
        padding: 0 15px;
        line-height: 20px;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        background: #ff5831;
        border-radius: 10px;
      }
    }
  }
}
.news {
  height: 100%;
  margin-top: 0 !important;
}
</style>
